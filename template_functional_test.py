#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板功能测试脚本
实际测试每个模板文件的Word生成功能
"""

import os
import json
import subprocess
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

class TemplateFunctionalTester:
    """模板功能测试器"""
    
    def __init__(self):
        self.template_files = [
            "word_zc/template_15players_backup_before_color_fix.docx",
            "word_zc/template_15players_backup.docx", 
            "word_zc/template_15players_clean.docx",
            "word_zc/template_15players_fixed.docx",
            "word_zc/template_15players.docx"
        ]
        self.jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
        self.test_results = {}
        
    def create_test_data(self):
        """创建标准测试数据"""
        return {
            "teamInfo": {
                "title": "功能测试报名表",
                "organizationName": "功能测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五",
                "contactPerson": "赵六",
                "contactPhone": "13800138000",
                "jerseyColor": "红色",
                "shortsColor": "蓝色", 
                "socksColor": "白色",
                "goalkeeperKitColor": "黄色"
            },
            "players": [
                {
                    "number": "1",
                    "name": "守门员",
                    "photoPath": "word_zc/ai-football-generator/java_word_photos/player1.png"
                },
                {
                    "number": "10",
                    "name": "队长",
                    "photoPath": "word_zc/ai-football-generator/java_word_photos/player2.jpg"
                },
                {
                    "number": "7",
                    "name": "前锋",
                    "photoPath": "word_zc/ai-football-generator/java_word_photos/player3.png"
                }
            ]
        }
    
    def test_template(self, template_path, test_name):
        """测试单个模板"""
        print(f"🧪 测试模板: {os.path.basename(template_path)}")
        
        if not os.path.exists(template_path):
            print(f"   ❌ 模板文件不存在")
            return {"success": False, "error": "文件不存在"}
        
        if not os.path.exists(self.jar_path):
            print(f"   ❌ JAR文件不存在: {self.jar_path}")
            return {"success": False, "error": "JAR文件不存在"}
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # 准备测试数据
                test_data = self.create_test_data()
                test_data["config"] = {
                    "templatePath": os.path.abspath(template_path),
                    "outputDir": temp_dir,
                    "photosDir": "word_zc/ai-football-generator/java_word_photos"
                }
                
                # 写入测试JSON文件
                test_json_path = os.path.join(temp_dir, f"test_{test_name}.json")
                with open(test_json_path, 'w', encoding='utf-8') as f:
                    json.dump(test_data, f, ensure_ascii=False, indent=2)
                
                print(f"   📝 测试数据已写入: {test_json_path}")
                
                # 执行Java程序
                cmd = [
                    "java", "-jar", os.path.abspath(self.jar_path),
                    test_json_path
                ]
                
                print(f"   🚀 执行命令: {' '.join(cmd)}")
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=os.getcwd()
                )
                
                # 检查输出文件
                output_files = list(Path(temp_dir).glob("*.docx"))
                
                test_result = {
                    "success": result.returncode == 0,
                    "return_code": result.returncode,
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "output_files": [str(f) for f in output_files],
                    "output_count": len(output_files)
                }
                
                if result.returncode == 0:
                    print(f"   ✅ 生成成功，输出文件数量: {len(output_files)}")
                    for output_file in output_files:
                        file_size = output_file.stat().st_size
                        print(f"      📄 {output_file.name} ({file_size:,} 字节)")
                else:
                    print(f"   ❌ 生成失败，返回码: {result.returncode}")
                    if result.stderr:
                        print(f"      错误信息: {result.stderr[:200]}...")
                
                return test_result
                
            except subprocess.TimeoutExpired:
                print(f"   ⏰ 测试超时")
                return {"success": False, "error": "超时"}
            except Exception as e:
                print(f"   ❌ 测试异常: {str(e)}")
                return {"success": False, "error": str(e)}
    
    def run_all_tests(self):
        """运行所有模板测试"""
        print("=" * 80)
        print("🧪 阶段3：功能测试")
        print("=" * 80)
        
        for i, template_file in enumerate(self.template_files):
            test_name = f"template_{i+1}"
            result = self.test_template(template_file, test_name)
            self.test_results[template_file] = result
            print()
    
    def generate_functional_report(self):
        """生成功能测试报告"""
        print("=" * 80)
        print("📊 功能测试报告")
        print("=" * 80)
        
        successful_templates = []
        failed_templates = []
        
        for template_file, result in self.test_results.items():
            template_name = os.path.basename(template_file)
            
            if result.get("success", False):
                successful_templates.append((template_name, result))
                print(f"✅ {template_name}")
                print(f"   输出文件数量: {result.get('output_count', 0)}")
            else:
                failed_templates.append((template_name, result))
                print(f"❌ {template_name}")
                print(f"   失败原因: {result.get('error', '未知错误')}")
            print()
        
        print("📈 测试统计:")
        print(f"   成功: {len(successful_templates)} 个模板")
        print(f"   失败: {len(failed_templates)} 个模板")
        print()
        
        if successful_templates:
            print("🏆 可用的模板文件:")
            for template_name, result in successful_templates:
                print(f"   ✅ {template_name}")
        
        return successful_templates, failed_templates

def main():
    """主函数"""
    print("🚀 模板功能测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = TemplateFunctionalTester()
    
    # 检查Java环境
    try:
        java_result = subprocess.run(["java", "-version"], 
                                   capture_output=True, text=True)
        if java_result.returncode == 0:
            print("✅ Java环境检查通过")
        else:
            print("❌ Java环境检查失败")
            return
    except Exception as e:
        print(f"❌ Java环境检查异常: {e}")
        return
    
    # 运行测试
    tester.run_all_tests()
    successful, failed = tester.generate_functional_report()
    
    print("✅ 功能测试完成")

if __name__ == "__main__":
    main()
