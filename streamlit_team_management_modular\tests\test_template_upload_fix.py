#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板上传修复
Test Template Upload Fix

验证模板上传后能立即在选择列表中显示
"""

import streamlit as st
import os
import sys
from PIL import Image
import io

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from components.template_selector import TemplateSelectorComponent
from services.template_service import TemplateService
from services.auth_service import AuthService

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (200, 200), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

def test_template_upload_and_selection():
    """测试模板上传和选择功能"""
    st.title("🧪 模板上传修复测试")
    
    # 设置测试用户
    if 'user_id' not in st.session_state:
        st.session_state.user_id = 'test_user_template_fix'
    
    user_id = st.session_state.user_id
    team_name = "测试球队"
    
    st.info(f"测试用户: {user_id}")
    st.info(f"测试球队: {team_name}")
    
    # 创建模板选择器
    template_selector = TemplateSelectorComponent(user_id)
    template_service = TemplateService(user_id)
    
    st.markdown("---")
    
    # 测试1: 显示当前模板列表
    st.markdown("### 📋 当前模板列表")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("#### 系统模板")
        system_templates = template_service.get_system_templates()
        total_system = sum(len(templates) for templates in system_templates.values())
        st.write(f"系统模板总数: {total_system}")
        
        for category, templates in system_templates.items():
            if templates:
                st.write(f"- {category}: {len(templates)} 个")
    
    with col2:
        st.markdown("#### 用户模板")
        user_templates = template_service.get_user_templates(team_name)
        total_user = sum(len(templates) for templates in user_templates.values())
        st.write(f"用户模板总数: {total_user}")
        
        for category, templates in user_templates.items():
            if templates:
                st.write(f"- {category}: {len(templates)} 个")
                for template in templates:
                    st.write(f"  * {template['name']}")
    
    st.markdown("---")
    
    # 测试2: 模拟上传模板
    st.markdown("### 📤 模拟上传测试")
    
    if st.button("🧪 创建测试模板"):
        # 创建测试图片
        test_img = create_test_image()
        
        # 模拟上传文件对象
        class MockUploadedFile:
            def __init__(self, content, name):
                self.content = content
                self.name = name
                self.size = len(content.getvalue())
            
            def getvalue(self):
                return self.content.getvalue()
        
        mock_file = MockUploadedFile(test_img, f"test_template_{len(user_templates['personal']) + 1}.png")
        
        # 保存模板
        saved_path = template_service.save_user_template(
            mock_file, mock_file.name, team_name, "team"
        )
        
        if saved_path:
            st.success(f"✅ 测试模板创建成功: {saved_path}")
            st.info("🔄 页面将自动刷新以显示新模板")
            st.rerun()
        else:
            st.error("❌ 测试模板创建失败")
    
    st.markdown("---")
    
    # 测试3: 使用修复后的模板选择器
    st.markdown("### 🎨 修复后的模板选择器")
    
    selected_template = template_selector.render_simple_template_upload(team_name)
    
    if selected_template:
        st.success(f"✅ 已选择模板: {os.path.basename(selected_template)}")
        st.info(f"📁 模板路径: {selected_template}")
        
        # 显示选中的模板预览
        try:
            img = Image.open(selected_template)
            st.image(img, caption="选中的模板", width=200)
        except Exception as e:
            st.error(f"无法显示模板预览: {e}")
    
    st.markdown("---")
    
    # 测试4: 清理测试数据
    st.markdown("### 🧹 清理测试数据")
    
    if st.button("🗑️ 清理所有测试模板", type="secondary"):
        try:
            auth_service = AuthService()
            user_data_path = auth_service.get_user_data_path(user_id)
            templates_path = os.path.join(user_data_path, "templates")
            
            if os.path.exists(templates_path):
                import shutil
                shutil.rmtree(templates_path)
                st.success("✅ 测试数据清理完成")
                st.rerun()
            else:
                st.info("📭 没有找到需要清理的测试数据")
        except Exception as e:
            st.error(f"❌ 清理失败: {e}")

def main():
    """主函数"""
    st.set_page_config(
        page_title="模板上传修复测试",
        page_icon="🧪",
        layout="wide"
    )
    
    test_template_upload_and_selection()

if __name__ == "__main__":
    main()
