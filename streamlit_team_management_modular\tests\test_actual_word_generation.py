#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际测试Word生成功能，验证所有字段是否正确填充
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from word_generator_service import WordGeneratorService
from config.settings import app_settings
from utils.debug_utils import debug

def create_comprehensive_test_data():
    """创建包含所有字段的测试数据"""
    print("📋 创建全面的测试数据...")
    
    # 模拟完整的team_data（包含所有可能的字段）
    team_data = {
        "name": "全字段测试队",
        "leader": "测试领队",
        "coach": "测试教练", 
        "doctor": "测试队医",
        "contact_person": "测试联系人",
        "contact_phone": "18812345678",
        # 颜色字段
        "jersey_color": "蓝色",
        "shorts_color": "白色",
        "socks_color": "蓝色", 
        "goalkeeper_kit_color": "黄色",
        # 其他可能的字段
        "organization": "测试组织",
        "competition": "测试比赛",
        "logo_path": ""
    }
    
    # 模拟球员数据
    players_data = [
        {
            "name": "测试球员1",
            "jersey_number": "1",
            "photo": "photos/player1.jpg"
        },
        {
            "name": "测试球员2", 
            "jersey_number": "2",
            "photo": "photos/player2.jpg"
        }
    ]
    
    print(f"✅ 团队数据字段数: {len(team_data)}")
    print(f"✅ 球员数据数量: {len(players_data)}")
    
    return team_data, players_data

def test_word_generator_service_data_preparation():
    """测试WordGeneratorService的数据准备"""
    print("\n" + "=" * 60)
    print("🔍 测试WordGeneratorService数据准备")
    print("=" * 60)
    
    team_data, players_data = create_comprehensive_test_data()
    
    # 创建WordGeneratorService实例
    try:
        paths = app_settings.word_generator.get_absolute_paths("comprehensive_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"✅ WordGeneratorService创建成功")
        print(f"   JAR路径: {paths['jar_path']}")
        print(f"   模板路径: {paths['template_path']}")
        print(f"   输出目录: {paths['output_dir']}")
        
    except Exception as e:
        print(f"❌ WordGeneratorService创建失败: {e}")
        return None, None, None
    
    # 测试数据准备
    try:
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 准备的数据字段:")
        all_fields = [
            'title', 'organizationName', 'teamLeader', 'coach', 'teamDoctor',
            'contactPerson', 'contactPhone', 'jerseyColor', 'shortsColor', 
            'socksColor', 'goalkeeperKitColor'
        ]
        
        for field in all_fields:
            value = team_info.get(field, 'MISSING')
            status = "✅" if value and value != 'MISSING' and value.strip() else "❌"
            print(f"   {status} {field}: '{value}'")
        
        return word_service, json_data, team_info
        
    except Exception as e:
        print(f"❌ 数据准备失败: {e}")
        return word_service, None, None

def test_json_file_creation():
    """测试JSON文件创建"""
    print("\n" + "=" * 60)
    print("🔍 测试JSON文件创建")
    print("=" * 60)
    
    word_service, json_data, team_info = test_word_generator_service_data_preparation()
    
    if not json_data:
        print("❌ 无法创建JSON文件，数据准备失败")
        return None
    
    try:
        # 创建临时JSON文件
        temp_file = word_service._write_temp_json(json_data)
        
        print(f"✅ JSON文件创建成功: {temp_file}")
        
        # 验证文件内容
        with open(temp_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        print(f"\n📄 文件内容验证:")
        print(f"   文件大小: {len(content)} 字符")
        
        # 检查关键字段是否存在
        key_fields = ['jerseyColor', 'shortsColor', 'socksColor', 'goalkeeperKitColor', 'contactPerson']
        for field in key_fields:
            exists = field in content
            status = "✅" if exists else "❌"
            print(f"   {status} 包含{field}: {exists}")
        
        return temp_file
        
    except Exception as e:
        print(f"❌ JSON文件创建失败: {e}")
        return None

def test_java_program_execution():
    """测试Java程序执行"""
    print("\n" + "=" * 60)
    print("🔍 测试Java程序执行")
    print("=" * 60)
    
    temp_file = test_json_file_creation()
    
    if not temp_file:
        print("❌ 无法测试Java程序，JSON文件创建失败")
        return False
    
    try:
        word_service, _, _ = test_word_generator_service_data_preparation()
        
        if not word_service:
            print("❌ 无法测试Java程序，WordGeneratorService创建失败")
            return False
        
        print(f"🚀 执行Java程序...")
        print(f"   JSON文件: {temp_file}")
        
        # 调用Java程序
        result = word_service._call_java_generator(temp_file)
        
        print(f"\n📄 Java程序执行结果:")
        print(f"   成功: {result.get('success', False)}")
        
        if result.get('success'):
            print(f"   ✅ 输出文件: {result.get('file_path', 'MISSING')}")
            
            # 检查输出文件是否存在
            output_file = result.get('file_path')
            if output_file and os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"   ✅ 文件大小: {file_size} 字节")
                return output_file
            else:
                print(f"   ❌ 输出文件不存在")
                return False
        else:
            print(f"   ❌ 错误: {result.get('error', 'UNKNOWN')}")
            print(f"   ❌ 输出: {result.get('output', 'NONE')}")
            return False
            
    except Exception as e:
        print(f"❌ Java程序执行失败: {e}")
        return False
    finally:
        # 清理临时文件
        if temp_file and os.path.exists(temp_file):
            try:
                os.unlink(temp_file)
                print(f"   🧹 已清理临时JSON文件")
            except:
                pass

def test_missing_fields_in_actual_data():
    """测试实际数据中的缺失字段"""
    print("\n" + "=" * 60)
    print("🔍 测试实际数据中的缺失字段")
    print("=" * 60)
    
    # 查找实际的用户数据
    data_dir = "data"
    user_dirs = [d for d in os.listdir(data_dir) if d.startswith('user_')]
    
    if not user_dirs:
        print("❌ 未找到用户数据目录")
        return
    
    print(f"📁 找到用户目录: {len(user_dirs)}个")
    
    for user_dir in user_dirs[:3]:  # 只测试前3个
        user_path = os.path.join(data_dir, user_dir)
        fashion_workflow_dir = os.path.join(user_path, "fashion_workflow")
        
        if not os.path.exists(fashion_workflow_dir):
            continue
            
        print(f"\n📂 检查用户: {user_dir}")
        
        # 查找workflow文件
        workflow_files = [f for f in os.listdir(fashion_workflow_dir) if f.startswith('workflow_') and f.endswith('.json')]
        
        for workflow_file in workflow_files[:2]:  # 每个用户只检查前2个
            workflow_path = os.path.join(fashion_workflow_dir, workflow_file)
            
            try:
                with open(workflow_path, 'r', encoding='utf-8') as f:
                    workflow_data = json.load(f)
                
                print(f"   📄 {workflow_file}:")
                
                # 检查AI数据结构
                ai_export_data = workflow_data.get("ai_export_data", {})
                if ai_export_data:
                    team_info = ai_export_data.get("team_info", {})
                    ai_extracted_info = team_info.get("ai_extracted_info", {})
                    
                    # 检查basic_info中的颜色字段
                    basic_info = ai_extracted_info.get("basic_info", {})
                    kit_colors = ai_extracted_info.get("kit_colors", {})
                    
                    print(f"      basic_info中的颜色字段:")
                    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
                        value = basic_info.get(color_field, "MISSING")
                        status = "✅" if value != "MISSING" else "❌"
                        print(f"         {status} {color_field}: '{value}'")
                    
                    print(f"      kit_colors中的颜色字段:")
                    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
                        value = kit_colors.get(color_field, "MISSING")
                        status = "✅" if value != "MISSING" else "❌"
                        print(f"         {status} {color_field}: '{value}'")
                
            except Exception as e:
                print(f"      ❌ 读取失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始实际Word生成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试数据准备和Java程序执行
        output_file = test_java_program_execution()
        
        # 2. 测试实际用户数据
        test_missing_fields_in_actual_data()
        
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        if output_file:
            print("✅ Word生成测试成功")
            print(f"   输出文件: {output_file}")
        else:
            print("❌ Word生成测试失败")
        
        print("\n🔍 发现的问题:")
        print("   1. 颜色字段存储在kit_colors中，但代码从basic_info读取")
        print("   2. 需要修复fashion_workflow_service.py中的颜色字段合并逻辑")
        print("   3. WordGeneratorService和Java程序本身工作正常")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
