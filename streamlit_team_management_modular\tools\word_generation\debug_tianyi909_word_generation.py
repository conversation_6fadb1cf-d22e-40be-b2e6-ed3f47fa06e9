#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试天依909的Word生成问题
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tianyi909_word_generation():
    """测试天依909的Word生成"""
    print("=" * 60)
    print("🧪 测试天依909的Word生成")
    print("=" * 60)
    
    try:
        # 读取天依909的数据
        test_file = "data/user_f33368cb41dd/enhanced_ai_data/天依909_ai_data.json"
        
        with open(test_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        extracted_info = data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        print(f"📄 天依909原始数据:")
        print(f"   basic_info: {basic_info}")
        print(f"   kit_colors: {kit_colors}")
        print(f"   additional_info: {additional_info}")
        
        # 准备Word生成数据（模拟工作流的处理）
        team_data = {
            "name": basic_info.get("team_name", "天依909"),
            "contact_person": basic_info.get("contact_person"),
            "contact_phone": basic_info.get("contact_phone"),
            "leader": basic_info.get("leader_name"),
            "coach": additional_info.get("coach_name", basic_info.get("leader_name")),
            "doctor": basic_info.get("team_doctor"),
            "jersey_color": kit_colors.get("jersey_color"),
            "shorts_color": kit_colors.get("shorts_color"),
            "socks_color": kit_colors.get("socks_color"),
            "goalkeeper_kit_color": kit_colors.get("goalkeeper_kit_color")
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"\n📄 准备的team_data:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 检查数据完整性
        empty_fields = []
        for key, value in team_data.items():
            if not value or value in ["", None, "自动填充", "待定", "未知", "暂无"]:
                empty_fields.append(key)
        
        if empty_fields:
            print(f"\n❌ 发现空字段: {empty_fields}")
        else:
            print(f"\n✅ 所有字段都有值")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("tianyi909_debug", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查WordGeneratorService准备的JSON数据
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 WordGeneratorService准备的JSON数据:")
        team_info = json_data.get("teamInfo", {})
        for key, value in team_info.items():
            print(f"   {key}: '{value}'")
        
        # 检查JSON数据中的空值
        json_empty_fields = []
        for key, value in team_info.items():
            if not value or value in ["", None, "null"]:
                json_empty_fields.append(key)
        
        if json_empty_fields:
            print(f"\n❌ JSON数据中的空字段: {json_empty_fields}")
        else:
            print(f"\n✅ JSON数据完整")
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的Word文档
            return analyze_tianyi909_document(output_file, team_info)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_tianyi909_document(docx_path, expected_data):
    """分析天依909生成的Word文档"""
    print(f"\n📄 分析天依909生成的Word文档:")
    
    try:
        import zipfile
        import re
        
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找未替换的占位符
        placeholder_pattern = r'\{([^}]+)\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"❌ 发现 {len(remaining_placeholders)} 个未替换的占位符:")
            unique_remaining = list(set(remaining_placeholders))
            for placeholder in unique_remaining:
                print(f"   {placeholder}")
        else:
            print(f"✅ 所有占位符都已替换")
        
        # 检查期望的数据是否出现在文档中
        print(f"\n📄 检查期望数据是否出现:")
        missing_data = []
        for key, value in expected_data.items():
            if value and str(value) in content:
                print(f"   ✅ {key}: '{value}' 已出现")
            else:
                print(f"   ❌ {key}: '{value}' 未出现")
                missing_data.append((key, value))
        
        # 显示文档中包含"天依909"、"赵六"、"粉色"等关键词的文本
        text_pattern = r'<w:t[^>]*>([^<]*)</w:t>'
        texts = re.findall(text_pattern, content)
        
        keywords = ["天依909", "赵六", "粉色", "黑色", "绿色", "18454432036"]
        found_keywords = []
        
        print(f"\n📄 搜索关键词:")
        for keyword in keywords:
            if any(keyword in text for text in texts):
                found_keywords.append(keyword)
                print(f"   ✅ 找到: '{keyword}'")
            else:
                print(f"   ❌ 未找到: '{keyword}'")
        
        # 显示文档中的所有有意义的文本（过滤空白和XML）
        meaningful_texts = []
        for text in texts:
            clean_text = text.strip()
            if clean_text and len(clean_text) > 1 and not clean_text.startswith('<'):
                meaningful_texts.append(clean_text)
        
        print(f"\n📄 文档中的所有有意义文本（前30个）:")
        for i, text in enumerate(meaningful_texts[:30]):
            print(f"   {i+1}. '{text}'")
        
        # 检查是否有空的表格单元格
        empty_cell_pattern = r'<w:tc[^>]*>.*?<w:t[^>]*></w:t>.*?</w:tc>'
        empty_cells = re.findall(empty_cell_pattern, content, re.DOTALL)
        
        if empty_cells:
            print(f"\n⚠️ 发现 {len(empty_cells)} 个可能的空表格单元格")
        
        return len(missing_data) == 0 and len(found_keywords) >= 3
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return False

def test_with_manual_data():
    """使用手动数据测试"""
    print(f"\n" + "=" * 60)
    print("🧪 使用手动数据测试")
    print("=" * 60)
    
    try:
        # 手动构造完整的数据
        team_data = {
            "name": "天依909",
            "contact_person": "赵六",
            "contact_phone": "18454432036",
            "leader": "赵六",
            "coach": "赵六",
            "doctor": "赵六",
            "jersey_color": "粉色",
            "shorts_color": "黑色",
            "socks_color": "粉色",
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "张三", "jersey_number": "1", "photo": ""}
        ]
        
        print(f"📄 手动构造的数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("manual_tianyi909", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 简单检查生成的文档
            import zipfile
            import re
            
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            keywords = ["天依909", "赵六", "粉色", "黑色"]
            found_count = 0
            
            for keyword in keywords:
                if keyword in content:
                    found_count += 1
                    print(f"   ✅ 找到: '{keyword}'")
                else:
                    print(f"   ❌ 未找到: '{keyword}'")
            
            return found_count == len(keywords)
        else:
            print(f"\n❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 调试天依909的Word生成问题")
    print(f"调试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试天依909的实际数据
        tianyi909_ok = test_tianyi909_word_generation()
        
        # 2. 使用手动数据测试
        manual_ok = test_with_manual_data()
        
        print("\n" + "=" * 60)
        print("📋 调试总结")
        print("=" * 60)
        
        print(f"📊 测试结果:")
        print(f"   天依909数据测试: {'✅ 成功' if tianyi909_ok else '❌ 失败'}")
        print(f"   手动数据测试: {'✅ 成功' if manual_ok else '❌ 失败'}")
        
        if not tianyi909_ok and manual_ok:
            print(f"\n🎯 问题诊断:")
            print(f"   手动数据测试成功，说明Word生成机制正常")
            print(f"   天依909数据测试失败，说明问题在数据处理环节")
            
            print(f"\n💡 可能的原因:")
            print(f"   1. 天依909的数据在传递过程中丢失")
            print(f"   2. 数据格式转换有问题")
            print(f"   3. 工作流处理逻辑有bug")
            print(f"   4. 数据验证过于严格，过滤了有效数据")
            
        elif tianyi909_ok and manual_ok:
            print(f"\n✅ 所有测试都成功!")
            print(f"   天依909的数据和Word生成都正常")
            print(f"   用户看到的问题可能已经解决")
            
        elif not tianyi909_ok and not manual_ok:
            print(f"\n❌ 所有测试都失败!")
            print(f"   Word生成机制本身有问题")
            print(f"   需要检查Java程序和模板文件")
            
        else:
            print(f"\n⚠️ 测试结果不一致")
            print(f"   需要进一步调查")
        
    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
