#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最新生成的Word文档
"""

import zipfile
import xml.etree.ElementTree as ET
import os

def check_latest_word_document():
    """检查最新生成的Word文档"""
    print("🔍 检查最新生成的Word文档")
    print("=" * 60)
    
    word_file = "data/user_e358df6703a9/word_output/足球队_registration_1756545334192.docx"
    
    if not os.path.exists(word_file):
        print(f"❌ Word文件不存在: {word_file}")
        return False
    
    try:
        with zipfile.ZipFile(word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print("📄 Word文档内容分析:")
                    
                    # 检查联系人信息
                    has_contact_person_zhaoliu = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person_zhaoliu else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 检查占位符是否还存在
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   {{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    # 显示联系人相关的上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    contact_found = False
                    
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            contact_found = True
                            break
                    
                    if not contact_found:
                        print("   ❌ 未找到联系人相关内容")
                        
                        # 搜索可能的联系人相关内容
                        contact_keywords = ["联系", "电话", "赵六", "18454432036", "contact"]
                        for keyword in contact_keywords:
                            if keyword in full_text:
                                # 找到关键词的位置
                                words = full_text.split()
                                for i, word in enumerate(words):
                                    if keyword in word:
                                        start = max(0, i-5)
                                        end = min(len(words), i+10)
                                        context = ' '.join(words[start:end])
                                        print(f"   找到'{keyword}': {context}")
                                        break
                                break
                    
                    # 显示文档的主要内容结构
                    print(f"\n📄 文档主要内容:")
                    lines = full_text.split('\n')
                    content_lines = [line.strip() for line in lines if line.strip()]
                    
                    for i, line in enumerate(content_lines[:15]):  # 显示前15行
                        if line:
                            print(f"   第{i+1}行: {line}")
                    
                    # 最终结论
                    print(f"\n🎯 检查结果:")
                    
                    if has_contact_person_zhaoliu and has_contact_phone:
                        print("🎉 联系人信息修复成功！")
                        print("✅ Word文档中正确显示联系人信息")
                        print("✅ 联系人信息自动化功能完全正常")
                        
                        print(f"\n📋 显示内容:")
                        print(f"   联系人: 赵六")
                        print(f"   电话: 18454432036")
                        
                        print(f"\n🎉 恭喜！问题已完全解决！")
                        return True
                        
                    elif not has_placeholder_person and not has_placeholder_phone:
                        print("⚠️ 占位符已替换但联系人信息为空")
                        print("💡 可能的原因:")
                        print("   1. 数据映射仍有问题")
                        print("   2. AI数据格式不匹配")
                        return False
                        
                    else:
                        print("❌ 占位符未被替换")
                        print("💡 可能的原因:")
                        print("   1. 模板占位符格式问题")
                        print("   2. Word生成服务问题")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 检查最新生成的Word文档")
    print("=" * 70)
    print("检查用户重启应用后生成的Word文档")
    print("=" * 70)
    
    result = check_latest_word_document()
    
    if result:
        print(f"\n🎉 最终结论: 联系人信息问题已完全解决！")
        print(f"✅ 用户可以正常使用联系人信息自动化功能")
        print(f"✅ 从AI聊天到Word生成的完整流程正常工作")
    else:
        print(f"\n❌ 仍需要进一步调试")

if __name__ == "__main__":
    main()
