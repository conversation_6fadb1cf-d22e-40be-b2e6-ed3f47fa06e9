#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于环境变量的配置文件
"""

import os
from dataclasses import dataclass
from typing import Dict, Any


# 导入主配置中的WordGeneratorSettings
from config.settings import WordGeneratorSettings

# 创建支持环境变量的配置实例
def create_env_word_settings():
    """创建支持环境变量覆盖的Word生成器配置"""
    # 如果设置了环境变量，则覆盖默认模板路径
    template_path = os.getenv("WORD_TEMPLATE_PATH")
    if template_path:
        # 这里可以动态修改配置，但保持类定义统一
        pass

    # 使用主配置中的统一定义
    return WordGeneratorSettings()

# 环境变量配置实例
env_word_settings = create_env_word_settings()
