#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后测试脚本
Post-Fix Test Script
"""

import os
import json

def test_fixed_logic():
    """测试修复后的逻辑"""
    print("🧪 测试修复后的逻辑")
    print("=" * 40)
    
    user_id = "37d53472d725"
    team_name = "003222"
    
    # 模拟修复后的照片路径逻辑
    team_file = f"data/user_{user_id}/teams/{team_name}.json"
    
    if os.path.exists(team_file):
        with open(team_file, 'r', encoding='utf-8') as f:
            team_data = json.load(f)
        
        for player in team_data.get('players', []):
            name = player.get('name', 'N/A')
            photo = player.get('photo', '')
            
            if photo:
                # 修复后的路径逻辑
                photo_path = f"data/user_{user_id}/photos/{team_name}/{photo}"
                exists = os.path.exists(photo_path)
                print(f"球员 {name}: {photo_path} {'✅' if exists else '❌'}")
                
                if exists:
                    print(f"   → 修复成功！照片路径正确")
                else:
                    print(f"   → 照片文件不存在")
    
    print("\n预期结果:")
    print("1. 照片路径解析正确")
    print("2. readiness检查返回True")
    print("3. 执行AI模式或手动模式都会自动生成Word")
    print("4. 换装完成后立即生成Word文档")

if __name__ == "__main__":
    test_fixed_logic()
