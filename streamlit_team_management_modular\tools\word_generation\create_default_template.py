#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建默认模板图片
Create Default Template Images

为系统创建一些基本的默认模板图片
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_default_football_jersey():
    """创建默认的足球球衣模板"""
    # 创建一个512x640的图片（标准尺寸）
    width, height = 512, 640
    
    # 创建白色背景
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制球衣轮廓（简单的T恤形状）
    # 球衣主体
    jersey_color = (0, 100, 200)  # 蓝色
    
    # 球衣主体矩形
    draw.rectangle([100, 150, 412, 500], fill=jersey_color)
    
    # 袖子
    draw.rectangle([50, 150, 100, 300], fill=jersey_color)  # 左袖
    draw.rectangle([412, 150, 462, 300], fill=jersey_color)  # 右袖
    
    # 领口
    draw.ellipse([200, 120, 312, 180], fill=jersey_color)
    draw.ellipse([210, 130, 302, 170], fill='white')  # 领口内部
    
    # 添加号码区域（白色矩形）
    draw.rectangle([200, 200, 312, 280], fill='white')
    draw.rectangle([202, 202, 310, 278], outline='black', width=2)
    
    # 添加文字说明
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 24)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    # 在号码区域添加文字
    draw.text((256, 230), "10", fill='black', font=font, anchor="mm")
    
    # 在底部添加说明文字
    try:
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        small_font = ImageFont.load_default()
    
    draw.text((256, 550), "默认足球球衣模板", fill='black', font=small_font, anchor="mm")
    
    return img

def create_default_casual_wear():
    """创建默认的休闲服装模板"""
    width, height = 512, 640
    
    # 创建白色背景
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制休闲T恤
    shirt_color = (150, 150, 150)  # 灰色
    
    # T恤主体
    draw.rectangle([120, 180, 392, 480], fill=shirt_color)
    
    # 袖子
    draw.rectangle([80, 180, 120, 280], fill=shirt_color)  # 左袖
    draw.rectangle([392, 180, 432, 280], fill=shirt_color)  # 右袖
    
    # 圆领
    draw.ellipse([220, 150, 292, 200], fill=shirt_color)
    draw.ellipse([225, 155, 287, 195], fill='white')
    
    # 添加简单的图案
    draw.ellipse([200, 250, 312, 350], outline='white', width=3)
    
    # 添加文字说明
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((256, 550), "默认休闲服装模板", fill='black', font=font, anchor="mm")
    
    return img

def create_default_formal_wear():
    """创建默认的正装模板"""
    width, height = 512, 640
    
    # 创建白色背景
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制西装
    suit_color = (50, 50, 50)  # 深灰色
    
    # 西装外套
    draw.rectangle([140, 200, 372, 500], fill=suit_color)
    
    # 袖子
    draw.rectangle([100, 200, 140, 400], fill=suit_color)  # 左袖
    draw.rectangle([372, 200, 412, 400], fill=suit_color)  # 右袖
    
    # 衬衫（白色）
    draw.rectangle([180, 180, 332, 300], fill='white')
    
    # 领带
    draw.rectangle([240, 180, 272, 350], fill=(150, 0, 0))  # 红色领带
    
    # 西装领子
    draw.polygon([(180, 180), (220, 150), (256, 180), (180, 220)], fill=suit_color)  # 左领
    draw.polygon([(332, 180), (292, 150), (256, 180), (332, 220)], fill=suit_color)  # 右领
    
    # 添加文字说明
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    draw.text((256, 550), "默认正装模板", fill='black', font=font, anchor="mm")
    
    return img

def main():
    """主函数"""
    print("🎨 开始创建默认模板图片...")
    
    # 确保目录存在
    base_dir = "assets/default_clothes"
    categories = {
        "football_jerseys": create_default_football_jersey,
        "casual_wear": create_default_casual_wear,
        "formal_wear": create_default_formal_wear
    }
    
    for category, create_func in categories.items():
        category_dir = os.path.join(base_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        # 创建图片
        img = create_func()
        
        # 保存图片
        filename = f"default_{category}.png"
        filepath = os.path.join(category_dir, filename)
        img.save(filepath, "PNG")
        
        print(f"✅ 创建模板: {filepath}")
    
    print("🎉 默认模板创建完成！")

if __name__ == "__main__":
    main()
