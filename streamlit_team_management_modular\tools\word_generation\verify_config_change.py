#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证配置修改是否成功
"""

import os
import sys

def verify_config_change():
    """验证配置修改"""
    print("🔍 验证配置文件修改结果")
    print("=" * 50)
    
    try:
        # 清除模块缓存
        if 'config.settings' in sys.modules:
            del sys.modules['config.settings']
        
        # 重新导入配置
        from config.settings import app_settings
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        print(f"📄 当前模板路径: {paths['template_path']}")
        print(f"📁 JAR路径: {paths['jar_path']}")
        
        # 检查模板路径是否正确
        if "template_15players" in paths['template_path']:
            print("✅ 配置修改成功！已指向15人模板")
        else:
            print("❌ 配置修改失败！仍指向原模板")
            return False
        
        # 检查文件是否存在
        template_exists = os.path.exists(paths['template_path'])
        jar_exists = os.path.exists(paths['jar_path'])
        
        print(f"✅ 模板文件存在: {template_exists}")
        print(f"✅ JAR文件存在: {jar_exists}")
        
        if template_exists:
            file_size = os.path.getsize(paths['template_path'])
            print(f"📊 模板文件大小: {file_size:,} 字节")
        
        return template_exists and jar_exists
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_word_generation():
    """测试Word生成功能"""
    print("\n🧪 测试Word生成功能")
    print("=" * 50)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print("✅ Word生成服务创建成功")
        
        # 准备测试数据
        team_data = {
            'name': '验证配置修改测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print("📝 开始生成Word报名表...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print("🎉 Word报名表生成成功！")
            print(f"📄 文件路径: {result['file_path']}")
            print(f"⚽ 球队名称: {result['team_name']}")
            print(f"👥 球员数量: {result['player_count']}")
            
            # 检查生成的文件
            if os.path.exists(result['file_path']):
                file_size = os.path.getsize(result['file_path'])
                print(f"📊 文件大小: {file_size:,} 字节")
                return True
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word报名表生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 验证配置文件修改")
    print("=" * 60)
    
    # 1. 验证配置修改
    config_ok = verify_config_change()
    
    if not config_ok:
        print("\n❌ 配置验证失败，请检查配置文件")
        return
    
    # 2. 测试Word生成功能
    word_ok = test_word_generation()
    
    # 3. 显示最终结果
    print("\n📊 验证结果")
    print("=" * 60)
    
    if config_ok and word_ok:
        print("🎉 配置修改完全成功！")
        print("✅ 系统现在使用template_15players_fixed.docx作为默认模板")
        print("✅ Word生成功能正常工作")
        print("\n🚀 下一步:")
        print("1. 重启Streamlit应用")
        print("2. 测试完整的换装流程")
        print("3. 验证生成的Word文档使用了新模板")
    else:
        print("❌ 配置修改存在问题，请检查")

if __name__ == "__main__":
    main()
