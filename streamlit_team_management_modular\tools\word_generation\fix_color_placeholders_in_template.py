#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复模板中的颜色占位符格式
基于联系人信息修复的成功经验
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import shutil
import re

def fix_color_placeholders_in_template():
    """修复模板中的颜色占位符格式"""
    print("🔧 修复模板中的颜色占位符格式")
    print("=" * 60)
    print("基于联系人信息修复的成功经验")
    
    template_path = "../word_zc/template_15players_fixed.docx"
    backup_path = "../word_zc/template_15players_backup_before_color_fix.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    # 创建备份
    shutil.copy2(template_path, backup_path)
    print(f"✅ 已创建备份: {os.path.basename(backup_path)}")
    
    try:
        # 读取模板
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 原始内容长度: {len(content)} 字符")
        
        # 修复颜色占位符 - 使用与联系人修复相同的方法
        original_content = content
        
        # 1. 修复 jerseyColor
        print(f"\n🔧 修复 jerseyColor 占位符:")
        jersey_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>jerseyColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        jersey_replacement = '<w:t>{{jerseyColor}}</w:t>'
        
        jersey_matches = re.findall(jersey_pattern, content)
        if jersey_matches:
            content = re.sub(jersey_pattern, jersey_replacement, content)
            print(f"   ✅ 修复了 {len(jersey_matches)} 个 jerseyColor 占位符")
        else:
            print(f"   ⚠️ 未找到需要修复的 jerseyColor 占位符")
        
        # 2. 修复 shortsColor
        print(f"\n🔧 修复 shortsColor 占位符:")
        shorts_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>shortsColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        shorts_replacement = '<w:t>{{shortsColor}}</w:t>'
        
        shorts_matches = re.findall(shorts_pattern, content)
        if shorts_matches:
            content = re.sub(shorts_pattern, shorts_replacement, content)
            print(f"   ✅ 修复了 {len(shorts_matches)} 个 shortsColor 占位符")
        else:
            print(f"   ⚠️ 未找到需要修复的 shortsColor 占位符")
        
        # 3. 修复 socksColor
        print(f"\n🔧 修复 socksColor 占位符:")
        socks_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>socksColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        socks_replacement = '<w:t>{{socksColor}}</w:t>'
        
        socks_matches = re.findall(socks_pattern, content)
        if socks_matches:
            content = re.sub(socks_pattern, socks_replacement, content)
            print(f"   ✅ 修复了 {len(socks_matches)} 个 socksColor 占位符")
        else:
            print(f"   ⚠️ 未找到需要修复的 socksColor 占位符")
        
        # 4. 修复 goalkeeperKitColor
        print(f"\n🔧 修复 goalkeeperKitColor 占位符:")
        goalkeeper_pattern = r'<w:t>\{\{</w:t></w:r><w:proofErr w:type="spellStart"/><w:r w:rsidRPr="[^"]*"><w:t>goalkeeperKitColor</w:t></w:r><w:proofErr w:type="spellEnd"/><w:r w:rsidRPr="[^"]*"><w:t>\}\}</w:t>'
        goalkeeper_replacement = '<w:t>{{goalkeeperKitColor}}</w:t>'
        
        goalkeeper_matches = re.findall(goalkeeper_pattern, content)
        if goalkeeper_matches:
            content = re.sub(goalkeeper_pattern, goalkeeper_replacement, content)
            print(f"   ✅ 修复了 {len(goalkeeper_matches)} 个 goalkeeperKitColor 占位符")
        else:
            print(f"   ⚠️ 未找到需要修复的 goalkeeperKitColor 占位符")
        
        # 检查是否有修改
        if content != original_content:
            print(f"\n📄 内容已修改，准备保存")
            
            # 创建临时目录
            temp_dir = "temp_template_fix"
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
            os.makedirs(temp_dir)
            
            try:
                # 解压模板到临时目录
                with zipfile.ZipFile(template_path, 'r') as zip_file:
                    zip_file.extractall(temp_dir)
                
                # 写入修复后的document.xml
                with open(os.path.join(temp_dir, 'word', 'document.xml'), 'w', encoding='utf-8') as f:
                    f.write(content)
                
                # 重新打包
                with zipfile.ZipFile(template_path, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for root, dirs, files in os.walk(temp_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_name = os.path.relpath(file_path, temp_dir)
                            zip_file.write(file_path, arc_name)
                
                print(f"✅ 模板修复完成")
                
                # 验证修复结果
                return verify_color_placeholders_fix(template_path)
                
            finally:
                # 清理临时目录
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
        else:
            print(f"\n⚠️ 未发现需要修复的颜色占位符")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        # 恢复备份
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, template_path)
            print(f"✅ 已从备份恢复模板")
        return False

def verify_color_placeholders_fix(template_path):
    """验证颜色占位符修复结果"""
    print(f"\n🔍 验证颜色占位符修复结果")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 检查修复后的占位符
        expected_placeholders = [
            '{{jerseyColor}}',
            '{{shortsColor}}',
            '{{socksColor}}',
            '{{goalkeeperKitColor}}'
        ]
        
        print(f"📄 检查修复后的占位符:")
        all_fixed = True
        
        for placeholder in expected_placeholders:
            if placeholder in content:
                print(f"   ✅ {placeholder}: 格式正确")
            else:
                print(f"   ❌ {placeholder}: 仍有问题")
                all_fixed = False
        
        # 检查是否还有分割的占位符
        print(f"\n📄 检查是否还有分割的占位符:")
        split_patterns = [
            r'jerseyColor.*?spellEnd',
            r'shortsColor.*?spellEnd',
            r'socksColor.*?spellEnd',
            r'goalkeeperKitColor.*?spellEnd'
        ]
        
        has_split_placeholders = False
        for pattern in split_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"   ⚠️ 仍有分割的占位符: {pattern}")
                has_split_placeholders = True
        
        if not has_split_placeholders:
            print(f"   ✅ 无分割的占位符")
        
        if all_fixed and not has_split_placeholders:
            print(f"\n🎉 颜色占位符修复完全成功！")
            return True
        else:
            print(f"\n⚠️ 颜色占位符修复部分成功")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_fixed_color_placeholders():
    """测试修复后的颜色占位符"""
    print(f"\n🔍 测试修复后的颜色占位符")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_44ecbeed9db2'
        
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 创建包含颜色信息的测试数据
        team_data = {
            'name': '颜色修复验证队',
            'leader': '测试领队',
            'coach': '测试教练',
            'doctor': '测试队医',
            'contact_person': '测试联系人',
            'contact_phone': '13800138000',
            # 颜色字段
            'jersey_color': '红色',
            'shorts_color': '蓝色',
            'socks_color': '白色',
            'goalkeeper_kit_color': '黄色'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '1',
                'photo': 'test.jpg'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   球衣颜色: '{team_data['jersey_color']}'")
        print(f"   球裤颜色: '{team_data['shorts_color']}'")
        print(f"   球袜颜色: '{team_data['socks_color']}'")
        print(f"   守门员服装颜色: '{team_data['goalkeeper_kit_color']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("color_fix_verify", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 生成Word
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_colors_in_generated_word(result['file_path'], team_data)
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_colors_in_generated_word(file_path, team_data):
    """检查生成的Word中的颜色"""
    print(f"\n🔍 检查生成的Word中的颜色")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查各种颜色
                    colors = {
                        'jersey_color': team_data.get('jersey_color', ''),
                        'shorts_color': team_data.get('shorts_color', ''),
                        'socks_color': team_data.get('socks_color', ''),
                        'goalkeeper_kit_color': team_data.get('goalkeeper_kit_color', '')
                    }
                    
                    print(f"📄 颜色修复验证:")
                    color_results = {}
                    
                    for color_type, color_value in colors.items():
                        if color_value:
                            has_color = color_value in full_text
                            print(f"   {color_type} '{color_value}': {'✅ 找到' if has_color else '❌ 未找到'}")
                            color_results[color_type] = has_color
                        else:
                            print(f"   {color_type}: ❌ 数据中无此字段")
                            color_results[color_type] = False
                    
                    # 计算成功率
                    successful_colors = sum(1 for result in color_results.values() if result)
                    total_colors = len(color_results)
                    success_rate = (successful_colors / total_colors * 100) if total_colors > 0 else 0
                    
                    print(f"\n📊 颜色字段修复验证结果: {successful_colors}/{total_colors} ({success_rate:.1f}%)")
                    
                    if success_rate == 100:
                        print(f"🎉 颜色字段修复完全成功！")
                        return True
                    elif success_rate >= 50:
                        print(f"⚠️ 颜色字段修复部分成功")
                        return False
                    else:
                        print(f"❌ 颜色字段修复失败")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 修复模板中的颜色占位符")
    print("=" * 70)
    print("基于联系人信息修复的成功经验")
    print("=" * 70)
    
    # 1. 修复模板中的颜色占位符
    fix_success = fix_color_placeholders_in_template()
    
    if fix_success:
        # 2. 测试修复后的颜色占位符
        test_success = test_fixed_color_placeholders()
        
        # 综合结果
        print(f"\n📊 颜色占位符修复结果")
        print("=" * 70)
        
        if test_success:
            print("🎉 颜色占位符修复完全成功！")
            print("✅ 模板中的颜色占位符格式已修复")
            print("✅ Word生成中的颜色字段正常工作")
            print("✅ 基于联系人信息修复经验的方法有效")
            
            print(f"\n🎯 现在用户可以:")
            print(f"   1. 在Word报名表中看到正确的球衣颜色")
            print(f"   2. 在Word报名表中看到正确的球裤颜色")
            print(f"   3. 在Word报名表中看到正确的球袜颜色")
            print(f"   4. 在Word报名表中看到正确的守门员服装颜色")
        else:
            print("⚠️ 颜色占位符修复部分成功")
            print("✅ 模板格式已修复")
            print("❌ Word生成仍有问题，需要进一步调试")
    else:
        print("❌ 颜色占位符修复失败")
        print("💡 建议检查模板文件和修复逻辑")

if __name__ == "__main__":
    main()
