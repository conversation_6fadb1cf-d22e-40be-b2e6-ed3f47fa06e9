#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面搜索所有引用旧模板的地方
"""

import os
import re
import json
from datetime import datetime

def search_all_template_references():
    """搜索所有模板引用"""
    print("=" * 60)
    print("🔍 全面搜索所有引用旧模板的地方")
    print("=" * 60)
    
    # 要搜索的目录
    search_dirs = [
        ".",  # 当前目录
        "../word_zc",  # word_zc目录
    ]
    
    # 要搜索的文件类型
    file_extensions = ['.py', '.json', '.properties', '.md', '.txt', '.yml', '.yaml', '.cfg', '.ini']
    
    # 搜索模式
    old_template_patterns = [
        r'template_15players\.docx',
        r'template_15players\.docx',
        r'"template_15players\.docx"',
        r"'template_15players\.docx'",
        r'template\.path.*template_15players',
        r'templatePath.*template_15players',
        r'TEMPLATE_PATH.*template_15players'
    ]
    
    found_references = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            continue
            
        print(f"\n📁 搜索目录: {search_dir}")
        
        for root, dirs, files in os.walk(search_dir):
            # 跳过一些目录
            dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules', 'target']]
            
            for file in files:
                if any(file.endswith(ext) for ext in file_extensions):
                    file_path = os.path.join(root, file)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        lines = content.split('\n')
                        
                        for i, line in enumerate(lines, 1):
                            for pattern in old_template_patterns:
                                if re.search(pattern, line, re.IGNORECASE):
                                    found_references.append({
                                        'file': file_path,
                                        'line': i,
                                        'content': line.strip(),
                                        'pattern': pattern
                                    })
                                    
                    except Exception as e:
                        continue
    
    return found_references

def categorize_references(references):
    """分类引用"""
    print(f"\n📊 找到 {len(references)} 个引用旧模板的地方:")
    
    categories = {
        'config_files': [],
        'test_files': [],
        'json_files': [],
        'python_files': [],
        'other_files': []
    }
    
    for ref in references:
        file_path = ref['file']
        
        if 'config' in file_path.lower() or file_path.endswith('.properties'):
            categories['config_files'].append(ref)
        elif 'test' in file_path.lower():
            categories['test_files'].append(ref)
        elif file_path.endswith('.json'):
            categories['json_files'].append(ref)
        elif file_path.endswith('.py'):
            categories['python_files'].append(ref)
        else:
            categories['other_files'].append(ref)
    
    return categories

def display_categorized_references(categories):
    """显示分类的引用"""
    
    for category, refs in categories.items():
        if refs:
            print(f"\n📂 {category.upper().replace('_', ' ')} ({len(refs)} 个):")
            
            for ref in refs:
                print(f"   📄 {ref['file']}:{ref['line']}")
                print(f"      {ref['content']}")
                print(f"      匹配模式: {ref['pattern']}")
                print()

def generate_fix_commands(categories):
    """生成修复命令"""
    print(f"\n🔧 修复建议:")
    
    fix_commands = []
    
    for category, refs in categories.items():
        if refs:
            print(f"\n📂 {category.upper().replace('_', ' ')}:")
            
            for ref in refs:
                file_path = ref['file']
                line_num = ref['line']
                content = ref['content']
                
                # 生成修复建议
                if 'template_15players_fixed.docx' in content:
                    new_content = content.replace('template_15players_fixed.docx', 'template_15players_fixed.docx')
                    
                    fix_commands.append({
                        'file': file_path,
                        'line': line_num,
                        'old': content.strip(),
                        'new': new_content.strip()
                    })
                    
                    print(f"   📄 {file_path}:{line_num}")
                    print(f"      旧: {content.strip()}")
                    print(f"      新: {new_content.strip()}")
                    print()
    
    return fix_commands

def apply_fixes(fix_commands):
    """应用修复"""
    print(f"\n🔧 应用修复 ({len(fix_commands)} 个文件):")
    
    fixed_files = []
    
    for fix in fix_commands:
        file_path = fix['file']
        old_content = fix['old']
        new_content = fix['new']
        
        try:
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换内容
            if old_content in content:
                new_file_content = content.replace(old_content, new_content)
                
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_file_content)
                
                print(f"   ✅ 已修复: {file_path}")
                fixed_files.append(file_path)
            else:
                print(f"   ⚠️ 内容不匹配: {file_path}")
                
        except Exception as e:
            print(f"   ❌ 修复失败: {file_path} - {e}")
    
    return fixed_files

def verify_fixes():
    """验证修复结果"""
    print(f"\n🔍 验证修复结果:")
    
    # 重新搜索
    remaining_references = search_all_template_references()
    
    if remaining_references:
        print(f"❌ 仍有 {len(remaining_references)} 个引用旧模板:")
        for ref in remaining_references:
            print(f"   📄 {ref['file']}:{ref['line']}")
            print(f"      {ref['content']}")
    else:
        print(f"✅ 所有引用都已修复!")
    
    return len(remaining_references) == 0

def main():
    """主函数"""
    print("🚀 全面搜索和修复旧模板引用")
    print(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 搜索所有引用
        references = search_all_template_references()
        
        if not references:
            print("✅ 没有找到引用旧模板的地方!")
            return
        
        # 2. 分类引用
        categories = categorize_references(references)
        
        # 3. 显示分类结果
        display_categorized_references(categories)
        
        # 4. 生成修复命令
        fix_commands = generate_fix_commands(categories)
        
        # 5. 询问是否应用修复
        print(f"\n❓ 是否应用修复? (找到 {len(fix_commands)} 个需要修复的地方)")
        print("   输入 'y' 或 'yes' 确认修复")
        
        # 自动应用修复（在脚本中）
        print("🔧 自动应用修复...")
        
        # 6. 应用修复
        fixed_files = apply_fixes(fix_commands)
        
        # 7. 验证修复结果
        all_fixed = verify_fixes()
        
        print(f"\n📋 修复总结:")
        print(f"   找到引用: {len(references)} 个")
        print(f"   修复文件: {len(fixed_files)} 个")
        print(f"   修复状态: {'✅ 完成' if all_fixed else '❌ 未完成'}")
        
        if fixed_files:
            print(f"\n📄 已修复的文件:")
            for file_path in fixed_files:
                print(f"   {file_path}")
        
    except Exception as e:
        print(f"❌ 搜索过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
