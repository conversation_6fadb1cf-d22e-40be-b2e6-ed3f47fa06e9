#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复用户数据中的"自动填充"问题
"""

import os
import sys
import json
import shutil
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_auto_fill_in_data(data, path=""):
    """递归修复数据中的"自动填充"字符串"""
    fixed_count = 0
    
    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            
            if isinstance(value, str) and value == "自动填充":
                # 根据字段类型进行智能修复
                if "color" in key.lower() or "颜色" in key:
                    # 颜色字段：根据上下文推断
                    if "jersey" in key.lower() or "球衣" in key:
                        data[key] = ""  # 留空，让用户重新输入
                    elif "shorts" in key.lower() or "球裤" in key:
                        data[key] = "黑色"  # 默认黑色
                    elif "socks" in key.lower() or "球袜" in key:
                        data[key] = ""  # 留空，让系统自动填充
                    elif "goalkeeper" in key.lower() or "守门员" in key:
                        data[key] = "绿色"  # 默认绿色
                    else:
                        data[key] = ""
                elif any(person_field in key.lower() for person_field in ["leader", "doctor", "coach", "领队", "队医", "教练"]):
                    # 人员字段：留空，让系统自动填充
                    data[key] = ""
                else:
                    # 其他字段：留空
                    data[key] = ""
                
                fixed_count += 1
                print(f"   修复: {current_path} '自动填充' → '{data[key]}'")
            
            elif isinstance(value, (dict, list)):
                fixed_count += fix_auto_fill_in_data(value, current_path)
    
    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            if isinstance(item, (dict, list)):
                fixed_count += fix_auto_fill_in_data(item, current_path)
    
    return fixed_count

def fix_json_file(file_path):
    """修复单个JSON文件"""
    print(f"\n📄 修复文件: {os.path.relpath(file_path)}")
    
    try:
        # 备份原文件
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"   ✅ 已备份到: {os.path.basename(backup_path)}")
        
        # 读取原数据
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 修复数据
        fixed_count = fix_auto_fill_in_data(data)
        
        if fixed_count > 0:
            # 保存修复后的数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"   ✅ 修复完成，共修复 {fixed_count} 个'自动填充'字段")
            return True
        else:
            # 删除不必要的备份
            os.remove(backup_path)
            print(f"   ✅ 文件正常，无需修复")
            return False
            
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")
        return False

def find_and_fix_all_files():
    """查找并修复所有包含"自动填充"的文件"""
    print("=" * 60)
    print("🔧 查找并修复所有包含'自动填充'的文件")
    print("=" * 60)
    
    data_dir = "data"
    
    # 查找所有JSON文件
    json_files = []
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json'):
                json_files.append(os.path.join(root, file))
    
    print(f"📄 找到 {len(json_files)} 个JSON文件")
    
    # 检查并修复每个文件
    problematic_files = []
    fixed_files = []
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "自动填充" in content:
                problematic_files.append(json_file)
                
                # 修复文件
                if fix_json_file(json_file):
                    fixed_files.append(json_file)
                    
        except Exception as e:
            print(f"❌ 检查文件失败: {json_file} - {e}")
    
    return problematic_files, fixed_files

def test_fixed_data():
    """测试修复后的数据"""
    print(f"\n" + "=" * 60)
    print("🧪 测试修复后的数据")
    print("=" * 60)
    
    # 重新搜索"自动填充"
    data_dir = "data"
    remaining_issues = []
    
    for root, dirs, files in os.walk(data_dir):
        for file in files:
            if file.endswith('.json') and not file.endswith('.backup_'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if "自动填充" in content:
                        remaining_issues.append(file_path)
                        
                except Exception as e:
                    pass
    
    if remaining_issues:
        print(f"❌ 仍有 {len(remaining_issues)} 个文件包含'自动填充':")
        for file_path in remaining_issues[:5]:  # 只显示前5个
            print(f"   {os.path.relpath(file_path)}")
    else:
        print(f"✅ 所有文件已修复，未发现'自动填充'字符串")
    
    return len(remaining_issues) == 0

def create_test_word_document():
    """创建测试Word文档验证修复效果"""
    print(f"\n" + "=" * 60)
    print("📄 创建测试Word文档验证修复效果")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 创建测试数据
        team_data = {
            "name": "修复验证队",
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "leader": "测试联系人",
            "coach": "测试联系人", 
            "doctor": "测试联系人",
            "jersey_color": "粉色",
            "shorts_color": "黑色",
            "socks_color": "粉色",
            "goalkeeper_kit_color": "绿色"
        }
        
        players_data = [
            {"name": "测试球员1", "jersey_number": "1", "photo": ""},
            {"name": "测试球员2", "jersey_number": "2", "photo": ""}
        ]
        
        print(f"📄 测试数据:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 生成Word文档
        paths = app_settings.word_generator.get_absolute_paths("fix_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查生成的文档
            import zipfile
            
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            auto_fill_count = content.count("自动填充")
            
            if auto_fill_count > 0:
                print(f"❌ Word文档中仍有 {auto_fill_count} 个'自动填充'字符串")
                return False
            else:
                print(f"✅ Word文档中未发现'自动填充'字符串")
                
                # 检查颜色
                colors = ["粉色", "黑色", "绿色"]
                colors_found = [color for color in colors if color in content]
                print(f"🎨 找到颜色: {colors_found}")
                
                return len(colors_found) == len(colors)
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 修复用户数据中的'自动填充'问题")
    print(f"修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 查找并修复所有文件
        problematic_files, fixed_files = find_and_fix_all_files()
        
        # 2. 测试修复后的数据
        all_fixed = test_fixed_data()
        
        # 3. 创建测试Word文档
        word_test_passed = create_test_word_document()
        
        print("\n" + "=" * 60)
        print("📋 修复总结")
        print("=" * 60)
        
        print(f"📊 修复统计:")
        print(f"   发现问题文件: {len(problematic_files)}")
        print(f"   成功修复文件: {len(fixed_files)}")
        print(f"   数据修复完成: {'✅' if all_fixed else '❌'}")
        print(f"   Word测试通过: {'✅' if word_test_passed else '❌'}")
        
        if all_fixed and word_test_passed:
            print(f"\n🎉 修复完全成功!")
            print(f"   ✅ 所有'自动填充'字符串已清理")
            print(f"   ✅ Word生成功能正常")
            print(f"   ✅ 颜色信息正确显示")
        else:
            print(f"\n⚠️ 修复部分成功")
            if not all_fixed:
                print(f"   ❌ 仍有文件包含'自动填充'")
            if not word_test_passed:
                print(f"   ❌ Word生成测试未通过")
        
        print(f"\n💡 后续建议:")
        print(f"   1. 用户重新输入球队信息时，系统会自动填充正确的值")
        print(f"   2. 现有的'自动填充'字符串已被清理或替换为合理默认值")
        print(f"   3. AI服务已增强，不会再生成'自动填充'字符串")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
