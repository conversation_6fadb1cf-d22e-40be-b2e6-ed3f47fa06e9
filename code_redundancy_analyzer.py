#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级代码重复和冗余分析工具
基于Word生成问题修复总结，分析项目中可能导致LLM判断失误的问题
"""

import os
import re
import json
import hashlib
from pathlib import Path
from collections import defaultdict, Counter
from datetime import datetime
import difflib

class CodeRedundancyAnalyzer:
    """代码重复和冗余分析器"""
    
    def __init__(self):
        self.streamlit_dir = "streamlit_team_management_modular"
        self.word_zc_dir = "word_zc"
        self.analysis_results = {
            'duplicate_code': [],
            'redundant_files': [],
            'config_conflicts': [],
            'llm_confusion_sources': [],
            'statistics': {}
        }
        
    def analyze_duplicate_code(self):
        """分析代码重复"""
        print("=" * 80)
        print("🔍 阶段1：代码重复分析")
        print("=" * 80)
        
        # 收集所有Python文件
        python_files = []
        for directory in [self.streamlit_dir, self.word_zc_dir]:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        if file.endswith('.py'):
                            python_files.append(os.path.join(root, file))
        
        print(f"📁 找到 {len(python_files)} 个Python文件")
        
        # 分析函数重复
        function_signatures = defaultdict(list)
        class_signatures = defaultdict(list)
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 提取函数定义
                functions = re.findall(r'def\s+(\w+)\s*\([^)]*\):', content)
                for func in functions:
                    function_signatures[func].append(file_path)
                
                # 提取类定义
                classes = re.findall(r'class\s+(\w+)(?:\([^)]*\))?:', content)
                for cls in classes:
                    class_signatures[cls].append(file_path)
                    
            except Exception as e:
                continue
        
        # 找出重复的函数和类
        duplicate_functions = {name: files for name, files in function_signatures.items() if len(files) > 1}
        duplicate_classes = {name: files for name, files in class_signatures.items() if len(files) > 1}
        
        print(f"🔄 重复函数: {len(duplicate_functions)} 个")
        for func_name, files in duplicate_functions.items():
            print(f"   📝 {func_name}: {len(files)} 个文件")
            for file_path in files[:3]:  # 只显示前3个
                print(f"      • {file_path}")
            if len(files) > 3:
                print(f"      ... 还有 {len(files) - 3} 个文件")
        
        print(f"🔄 重复类: {len(duplicate_classes)} 个")
        for class_name, files in duplicate_classes.items():
            print(f"   📝 {class_name}: {len(files)} 个文件")
            for file_path in files[:3]:
                print(f"      • {file_path}")
            if len(files) > 3:
                print(f"      ... 还有 {len(files) - 3} 个文件")
        
        self.analysis_results['duplicate_code'] = {
            'functions': duplicate_functions,
            'classes': duplicate_classes
        }
        
        return duplicate_functions, duplicate_classes
    
    def analyze_redundant_files(self):
        """分析冗余文件"""
        print("\n" + "=" * 80)
        print("🗑️ 阶段2：冗余文件检测")
        print("=" * 80)
        
        redundant_patterns = [
            r'.*_backup\.py$',
            r'.*_old\.py$',
            r'.*_test\.py$',
            r'.*_copy\.py$',
            r'.*_original\.py$',
            r'.*_temp\.py$',
            r'.*_debug\.py$',
            r'test_.*\.py$',
            r'.*\.bak$',
            r'.*~$'
        ]
        
        redundant_files = []
        
        for directory in [self.streamlit_dir, self.word_zc_dir]:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        file_path = os.path.join(root, file)
                        for pattern in redundant_patterns:
                            if re.match(pattern, file, re.IGNORECASE):
                                redundant_files.append({
                                    'path': file_path,
                                    'pattern': pattern,
                                    'size': os.path.getsize(file_path),
                                    'modified': datetime.fromtimestamp(os.path.getmtime(file_path))
                                })
                                break
        
        print(f"🗑️ 发现 {len(redundant_files)} 个可能的冗余文件:")
        
        # 按模式分组
        by_pattern = defaultdict(list)
        for file_info in redundant_files:
            by_pattern[file_info['pattern']].append(file_info)
        
        for pattern, files in by_pattern.items():
            print(f"\n📋 模式 {pattern}: {len(files)} 个文件")
            for file_info in files[:5]:  # 只显示前5个
                print(f"   📄 {file_info['path']}")
                print(f"      大小: {file_info['size']:,} 字节")
                print(f"      修改: {file_info['modified']}")
            if len(files) > 5:
                print(f"   ... 还有 {len(files) - 5} 个文件")
        
        self.analysis_results['redundant_files'] = redundant_files
        return redundant_files
    
    def analyze_config_conflicts(self):
        """分析配置文件冲突"""
        print("\n" + "=" * 80)
        print("⚙️ 阶段3：配置文件冲突分析")
        print("=" * 80)
        
        config_files = []
        
        # 查找配置文件
        config_patterns = [
            r'.*settings.*\.py$',
            r'.*config.*\.py$',
            r'.*\.properties$',
            r'.*\.json$',
            r'.*\.yml$',
            r'.*\.yaml$'
        ]
        
        for directory in [self.streamlit_dir, self.word_zc_dir]:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        for pattern in config_patterns:
                            if re.match(pattern, file, re.IGNORECASE):
                                config_files.append(os.path.join(root, file))
                                break
        
        print(f"⚙️ 找到 {len(config_files)} 个配置文件:")
        
        # 分析配置内容
        config_contents = {}
        template_paths = []
        jar_paths = []
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    config_contents[config_file] = content
                    
                    # 查找模板路径配置
                    template_matches = re.findall(r'TEMPLATE_PATH.*?["\']([^"\']+)["\']', content)
                    for match in template_matches:
                        template_paths.append((config_file, match))
                    
                    # 查找JAR路径配置
                    jar_matches = re.findall(r'JAR_PATH.*?["\']([^"\']+)["\']', content)
                    for match in jar_matches:
                        jar_paths.append((config_file, match))
                        
                print(f"   📄 {config_file}")
                if template_matches:
                    print(f"      模板路径: {template_matches}")
                if jar_matches:
                    print(f"      JAR路径: {jar_matches}")
                    
            except Exception as e:
                print(f"   ❌ 读取失败: {config_file} - {e}")
        
        # 检查路径冲突
        template_conflicts = defaultdict(list)
        for file_path, template_path in template_paths:
            template_conflicts[template_path].append(file_path)
        
        jar_conflicts = defaultdict(list)
        for file_path, jar_path in jar_paths:
            jar_conflicts[jar_path].append(file_path)
        
        print(f"\n🔍 模板路径配置:")
        for template_path, files in template_conflicts.items():
            print(f"   📄 {template_path}: {len(files)} 个配置文件")
            if len(files) > 1:
                print(f"      ⚠️ 可能的冲突:")
                for file_path in files:
                    print(f"         • {file_path}")
        
        print(f"\n🔍 JAR路径配置:")
        for jar_path, files in jar_conflicts.items():
            print(f"   📄 {jar_path}: {len(files)} 个配置文件")
            if len(files) > 1:
                print(f"      ⚠️ 可能的冲突:")
                for file_path in files:
                    print(f"         • {file_path}")
        
        conflicts = {
            'template_conflicts': dict(template_conflicts),
            'jar_conflicts': dict(jar_conflicts)
        }
        
        self.analysis_results['config_conflicts'] = conflicts
        return conflicts

    def analyze_llm_confusion_sources(self):
        """分析可能导致LLM混淆的源头"""
        print("\n" + "=" * 80)
        print("🤖 阶段4：LLM混淆源分析")
        print("=" * 80)

        confusion_sources = []

        # 1. 分析相似命名的文件
        all_files = []
        for directory in [self.streamlit_dir, self.word_zc_dir]:
            if os.path.exists(directory):
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        if file.endswith(('.py', '.json', '.md')):
                            all_files.append(os.path.join(root, file))

        # 按文件名分组
        filename_groups = defaultdict(list)
        for file_path in all_files:
            filename = os.path.basename(file_path)
            base_name = filename.split('.')[0]
            filename_groups[base_name].append(file_path)

        similar_files = {name: files for name, files in filename_groups.items() if len(files) > 1}

        print(f"📝 相似命名文件组: {len(similar_files)} 组")
        for base_name, files in similar_files.items():
            if len(files) > 1:
                print(f"   📋 {base_name}: {len(files)} 个文件")
                for file_path in files:
                    print(f"      • {file_path}")
                confusion_sources.append({
                    'type': 'similar_naming',
                    'base_name': base_name,
                    'files': files,
                    'risk_level': 'high' if len(files) > 3 else 'medium'
                })

        # 2. 分析Word生成相关的重复逻辑
        word_related_files = []
        word_keywords = ['word', 'generator', 'template', 'document', 'docx']

        for file_path in all_files:
            filename = os.path.basename(file_path).lower()
            if any(keyword in filename for keyword in word_keywords):
                word_related_files.append(file_path)

        print(f"\n📄 Word生成相关文件: {len(word_related_files)} 个")
        for file_path in word_related_files:
            print(f"   • {file_path}")

        # 3. 分析配置文件版本混乱
        settings_files = [f for f in all_files if 'settings' in os.path.basename(f).lower()]
        config_files = [f for f in all_files if 'config' in os.path.basename(f).lower()]

        if len(settings_files) > 1:
            confusion_sources.append({
                'type': 'multiple_settings',
                'files': settings_files,
                'risk_level': 'high',
                'description': '多个settings文件可能导致配置混乱'
            })

        if len(config_files) > 1:
            confusion_sources.append({
                'type': 'multiple_configs',
                'files': config_files,
                'risk_level': 'high',
                'description': '多个config文件可能导致配置混乱'
            })

        # 4. 分析测试文件和生产文件混合
        test_files = [f for f in all_files if any(keyword in os.path.basename(f).lower()
                     for keyword in ['test', 'debug', 'temp', 'backup'])]

        print(f"\n🧪 测试/调试文件: {len(test_files)} 个")
        for file_path in test_files[:10]:  # 只显示前10个
            print(f"   • {file_path}")
        if len(test_files) > 10:
            print(f"   ... 还有 {len(test_files) - 10} 个文件")

        if test_files:
            confusion_sources.append({
                'type': 'test_production_mix',
                'files': test_files,
                'risk_level': 'medium',
                'description': '测试文件与生产文件混合可能导致LLM选择错误的代码'
            })

        self.analysis_results['llm_confusion_sources'] = confusion_sources
        return confusion_sources

    def generate_cleanup_report(self):
        """生成清理建议报告"""
        print("\n" + "=" * 80)
        print("📊 阶段5：生成清理建议报告")
        print("=" * 80)

        # 统计信息
        stats = {
            'total_duplicate_functions': len(self.analysis_results['duplicate_code'].get('functions', {})),
            'total_duplicate_classes': len(self.analysis_results['duplicate_code'].get('classes', {})),
            'total_redundant_files': len(self.analysis_results['redundant_files']),
            'total_confusion_sources': len(self.analysis_results['llm_confusion_sources']),
            'config_conflicts': len(self.analysis_results['config_conflicts'])
        }

        self.analysis_results['statistics'] = stats

        print(f"📈 统计摘要:")
        print(f"   🔄 重复函数: {stats['total_duplicate_functions']} 个")
        print(f"   🔄 重复类: {stats['total_duplicate_classes']} 个")
        print(f"   🗑️ 冗余文件: {stats['total_redundant_files']} 个")
        print(f"   🤖 LLM混淆源: {stats['total_confusion_sources']} 个")
        print(f"   ⚙️ 配置冲突: {stats['config_conflicts']} 个")

        # 生成优先级建议
        high_priority = []
        medium_priority = []
        low_priority = []

        # 高优先级：配置冲突和多个settings文件
        for source in self.analysis_results['llm_confusion_sources']:
            if source.get('risk_level') == 'high':
                high_priority.append(source)
            elif source.get('risk_level') == 'medium':
                medium_priority.append(source)
            else:
                low_priority.append(source)

        print(f"\n🚨 高优先级问题: {len(high_priority)} 个")
        for item in high_priority:
            print(f"   • {item['type']}: {item.get('description', '需要立即处理')}")

        print(f"\n⚠️ 中优先级问题: {len(medium_priority)} 个")
        for item in medium_priority:
            print(f"   • {item['type']}: {item.get('description', '建议处理')}")

        return {
            'statistics': stats,
            'high_priority': high_priority,
            'medium_priority': medium_priority,
            'low_priority': low_priority
        }

def main():
    """主函数"""
    print("🚀 企业级代码重复和冗余分析开始")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("基于Word生成问题修复总结，分析可能导致LLM判断失误的问题")

    analyzer = CodeRedundancyAnalyzer()

    # 执行分析
    analyzer.analyze_duplicate_code()
    analyzer.analyze_redundant_files()
    analyzer.analyze_config_conflicts()
    analyzer.analyze_llm_confusion_sources()
    report = analyzer.generate_cleanup_report()

    # 保存详细结果
    with open('code_redundancy_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analyzer.analysis_results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n💾 详细分析结果已保存到: code_redundancy_analysis_results.json")
    print("✅ 分析完成")

if __name__ == "__main__":
    main()
