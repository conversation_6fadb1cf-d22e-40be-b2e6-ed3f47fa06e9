# Word生成工具目录

这个目录包含Word文档生成相关的工具和辅助脚本。

## 文件类型
- `*template*.py` - 模板处理工具
- `check_*.py` - 检查和验证工具
- `analyze_*.py` - 分析工具
- `fix_*.py` - 修复工具
- `create_*.py` - 创建工具
- `verify_*.py` - 验证工具

## 核心生产文件位置
核心的Word生成功能文件仍在主目录中：
- `word_generator_service.py` - Word生成核心服务
- `services/fashion_workflow_service.py` - 工作流服务
- `config/settings.py` - 配置文件

## 使用说明
这些工具主要用于：
- Word模板的创建和修复
- 生成功能的调试和分析
- 模板格式的验证和检查

## 注意事项
- 工具文件与核心功能分离，提高代码清晰度
- 使用工具前请阅读具体文件的文档说明
- 部分工具可能需要特定的模板文件或配置
