#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板管理修复
Test Template Management Fixes

测试修复后的模板管理系统是否正常工作
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_default_templates():
    """测试默认模板"""
    print("🧪 测试默认模板")
    print("=" * 50)
    
    try:
        from services.template_service import TemplateService
        
        # 测试系统模板服务
        template_service = TemplateService()
        system_templates = template_service.get_system_templates()
        
        print(f"📦 系统模板类别数: {len(system_templates)}")
        
        total_templates = 0
        for category, templates in system_templates.items():
            print(f"  {category}: {len(templates)} 个模板")
            for template in templates:
                print(f"    - {template['name']}: {template['path']}")
                if os.path.exists(template['path']):
                    print(f"      ✅ 文件存在")
                    total_templates += 1
                else:
                    print(f"      ❌ 文件不存在")
                    return False
        
        if total_templates > 0:
            print(f"✅ 默认模板测试通过！共找到 {total_templates} 个模板")
            return True
        else:
            print("❌ 没有找到任何默认模板")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_path_handling():
    """测试模板路径处理"""
    print("\n🧪 测试模板路径处理")
    print("=" * 50)
    
    try:
        from data.file_manager import FileManager
        
        # 测试FileManager的路径处理
        test_user_id = "test_user_path"
        file_manager = FileManager(test_user_id)
        
        # 测试字符串路径处理
        test_path = "assets/default_clothes/football_jerseys/default_football_jerseys.png"
        if os.path.exists(test_path):
            result = file_manager.save_template_image(test_path, "test.png", "test_team")
            
            if result == test_path:
                print("✅ 字符串路径处理正确")
            else:
                print(f"❌ 字符串路径处理错误: 期望 {test_path}, 得到 {result}")
                return False
        else:
            print(f"⚠️ 测试文件不存在: {test_path}")
        
        # 测试不存在的路径
        invalid_path = "non_existent_path.png"
        result = file_manager.save_template_image(invalid_path, "test.png", "test_team")
        
        if result is None:
            print("✅ 无效路径处理正确")
        else:
            print(f"❌ 无效路径处理错误: 应该返回None, 得到 {result}")
            return False
        
        print("✅ 模板路径处理测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_default_clothes_integration():
    """测试默认衣服图片集成"""
    print("\n🧪 测试默认衣服图片集成")
    print("=" * 50)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        test_user_id = "test_user_workflow"
        workflow_service = FashionWorkflowService(test_user_id)
        
        # 测试获取默认衣服图片
        default_image = workflow_service._get_default_clothes_image()
        
        if default_image:
            print(f"✅ 找到默认衣服图片: {default_image}")
            
            if os.path.exists(default_image):
                print("✅ 默认衣服图片文件存在")
                return True
            else:
                print(f"❌ 默认衣服图片文件不存在: {default_image}")
                return False
        else:
            print("❌ 没有找到默认衣服图片")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_template_selector_return_type():
    """测试模板选择器返回类型"""
    print("\n🧪 测试模板选择器返回类型")
    print("=" * 50)
    
    try:
        from components.template_selector import TemplateSelectorComponent
        
        # 创建模板选择器
        test_user_id = "test_user_selector"
        selector = TemplateSelectorComponent(test_user_id)
        
        # 检查方法签名
        import inspect
        
        # 检查render_simple_template_upload方法
        sig = inspect.signature(selector.render_simple_template_upload)
        return_annotation = sig.return_annotation
        
        if return_annotation == "Optional[str]" or "str" in str(return_annotation):
            print("✅ render_simple_template_upload 返回类型正确")
        else:
            print(f"⚠️ render_simple_template_upload 返回类型: {return_annotation}")
        
        # 检查render_template_selector方法
        sig = inspect.signature(selector.render_template_selector)
        return_annotation = sig.return_annotation
        
        if return_annotation == "Optional[str]" or "str" in str(return_annotation):
            print("✅ render_template_selector 返回类型正确")
        else:
            print(f"⚠️ render_template_selector 返回类型: {return_annotation}")
        
        print("✅ 模板选择器返回类型测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_integration():
    """测试组件集成"""
    print("\n🧪 测试组件集成")
    print("=" * 50)
    
    try:
        from components.photo_processing import PhotoProcessingComponent
        from components.batch_upload import BatchUploadComponent
        
        # 测试PhotoProcessingComponent
        test_user_id = "test_user_component"
        photo_component = PhotoProcessingComponent()
        
        # 检查方法签名
        import inspect
        sig = inspect.signature(photo_component.render_template_upload)
        return_annotation = sig.return_annotation
        
        if "str" in str(return_annotation):
            print("✅ PhotoProcessingComponent.render_template_upload 返回类型正确")
        else:
            print(f"⚠️ PhotoProcessingComponent.render_template_upload 返回类型: {return_annotation}")
        
        # 测试BatchUploadComponent
        batch_component = BatchUploadComponent()
        
        sig = inspect.signature(batch_component.render_template_upload)
        return_annotation = sig.return_annotation
        
        if "str" in str(return_annotation):
            print("✅ BatchUploadComponent.render_template_upload 返回类型正确")
        else:
            print(f"⚠️ BatchUploadComponent.render_template_upload 返回类型: {return_annotation}")
        
        print("✅ 组件集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n🧪 测试文件结构")
    print("=" * 50)
    
    # 检查默认模板文件
    expected_files = [
        "assets/default_clothes/football_jerseys/default_football_jerseys.png",
        "assets/default_clothes/casual_wear/default_casual_wear.png",
        "assets/default_clothes/formal_wear/default_formal_wear.png"
    ]
    
    for file_path in expected_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            return False
    
    print("✅ 文件结构测试通过！")
    return True

def main():
    """主测试函数"""
    print("🚀 开始模板管理修复测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("文件结构", test_file_structure()))
    test_results.append(("默认模板", test_default_templates()))
    test_results.append(("模板路径处理", test_template_path_handling()))
    test_results.append(("默认衣服图片集成", test_default_clothes_integration()))
    test_results.append(("模板选择器返回类型", test_template_selector_return_type()))
    test_results.append(("组件集成", test_component_integration()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！模板管理修复成功。")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
