#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析Word模板内容，查找联系人信息的位置
"""

import zipfile
import xml.etree.ElementTree as ET
import re

def extract_full_template_content():
    """提取模板的完整内容"""
    print("🔍 详细分析Word模板内容")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            # 读取document.xml文件
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 搜索联系人相关内容:")
                
                # 搜索各种可能的联系人相关文本
                search_patterns = [
                    r'联系人',
                    r'联系电话',
                    r'contact',
                    r'phone',
                    r'电话',
                    r'手机',
                    r'{{.*?}}',  # 所有占位符
                    r'领队',
                    r'教练',
                    r'队医'
                ]
                
                found_content = {}
                
                for pattern in search_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        found_content[pattern] = matches
                        print(f"   ✅ 找到 '{pattern}': {matches[:5]}...")  # 只显示前5个
                    else:
                        print(f"   ❌ 未找到 '{pattern}'")
                
                # 查找所有占位符
                print(f"\n📄 所有占位符:")
                placeholders = re.findall(r'{{.*?}}', content)
                if placeholders:
                    for placeholder in set(placeholders):  # 去重
                        print(f"   • {placeholder}")
                else:
                    print("   ❌ 未找到任何占位符")
                
                # 查找表格结构
                print(f"\n📄 表格结构分析:")
                table_count = content.count('<w:tbl>')
                print(f"   表格数量: {table_count}")
                
                # 查找可能的联系人信息位置
                print(f"\n📄 可能的联系人信息位置:")
                
                # 分析XML结构，查找包含"联系"的段落
                try:
                    root = ET.fromstring(content)
                    
                    # 查找所有文本节点
                    text_nodes = []
                    for elem in root.iter():
                        if elem.text and ('联系' in elem.text or 'contact' in elem.text.lower()):
                            text_nodes.append(elem.text)
                    
                    if text_nodes:
                        print("   ✅ 找到包含'联系'的文本:")
                        for text in text_nodes[:10]:  # 只显示前10个
                            print(f"     • {text}")
                    else:
                        print("   ❌ 未找到包含'联系'的文本")
                        
                except ET.ParseError as e:
                    print(f"   ⚠️ XML解析错误: {e}")
                
                return content, found_content
                
    except Exception as e:
        print(f"❌ 模板分析失败: {e}")
        return None, None

def search_template_structure():
    """搜索模板的结构，查找应该放置联系人信息的位置"""
    print("\n🔍 搜索模板结构")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 查找表格和表单结构
                print("📄 查找表格结构:")
                
                # 分析表格内容
                table_pattern = r'<w:tbl>.*?</w:tbl>'
                tables = re.findall(table_pattern, content, re.DOTALL)
                
                print(f"   找到 {len(tables)} 个表格")
                
                for i, table in enumerate(tables):
                    print(f"\n   表格 {i+1}:")
                    
                    # 查找表格中的文本内容
                    text_pattern = r'<w:t[^>]*>(.*?)</w:t>'
                    texts = re.findall(text_pattern, table)
                    
                    # 过滤和清理文本
                    meaningful_texts = []
                    for text in texts:
                        if text.strip() and len(text.strip()) > 1:
                            meaningful_texts.append(text.strip())
                    
                    if meaningful_texts:
                        print(f"     包含文本: {meaningful_texts[:10]}")  # 只显示前10个
                        
                        # 检查是否包含联系人相关信息
                        contact_related = [t for t in meaningful_texts if 
                                         '联系' in t or '电话' in t or '手机' in t or 
                                         'contact' in t.lower() or 'phone' in t.lower()]
                        
                        if contact_related:
                            print(f"     ✅ 包含联系人相关: {contact_related}")
                        else:
                            print(f"     ❌ 不包含联系人相关信息")
                    else:
                        print(f"     ❌ 无有意义的文本内容")
                
                return tables
                
    except Exception as e:
        print(f"❌ 结构分析失败: {e}")
        return None

def analyze_existing_placeholders():
    """分析现有的占位符模式"""
    print("\n🔍 分析现有占位符模式")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 查找所有占位符
                placeholders = re.findall(r'{{.*?}}', content)
                
                if placeholders:
                    print("📄 现有占位符分析:")
                    
                    unique_placeholders = list(set(placeholders))
                    for placeholder in sorted(unique_placeholders):
                        print(f"   • {placeholder}")
                        
                        # 查找占位符的上下文
                        pattern = re.escape(placeholder)
                        context_pattern = f'.{{0,50}}{pattern}.{{0,50}}'
                        contexts = re.findall(context_pattern, content)
                        
                        if contexts:
                            # 清理XML标签
                            clean_context = re.sub(r'<[^>]+>', '', contexts[0])
                            if clean_context.strip():
                                print(f"     上下文: {clean_context.strip()}")
                    
                    # 分析占位符模式
                    print(f"\n📄 占位符模式分析:")
                    print(f"   总数: {len(placeholders)}")
                    print(f"   唯一: {len(unique_placeholders)}")
                    
                    # 检查是否有联系人相关的占位符
                    contact_placeholders = [p for p in unique_placeholders if 
                                          'contact' in p.lower() or '联系' in p]
                    
                    if contact_placeholders:
                        print(f"   ✅ 联系人占位符: {contact_placeholders}")
                    else:
                        print(f"   ❌ 无联系人占位符")
                        
                        # 建议应该添加的占位符
                        print(f"\n💡 建议添加的联系人占位符:")
                        print(f"   • {{{{contactPerson}}}}")
                        print(f"   • {{{{contactPhone}}}}")
                
                else:
                    print("❌ 模板中没有任何占位符")
                    print("💡 这可能是一个静态模板，需要手动添加占位符")
                
                return unique_placeholders if placeholders else []
                
    except Exception as e:
        print(f"❌ 占位符分析失败: {e}")
        return []

def suggest_template_fixes():
    """建议模板修复方案"""
    print("\n💡 模板修复建议")
    print("=" * 60)
    
    print("🎯 问题确认:")
    print("   ❌ Word模板中缺少 {{contactPerson}} 和 {{contactPhone}} 占位符")
    print("   ✅ Java代码已经准备好处理这些字段")
    print("   ✅ Python数据映射已经正确")
    
    print("\n🔧 修复方案:")
    print("   方案1: 手动编辑Word模板")
    print("     • 打开 template_15players_fixed.docx")
    print("     • 在适当位置添加 {{contactPerson}} 和 {{contactPhone}}")
    print("     • 保存模板")
    
    print("\n   方案2: 检查是否有其他模板文件")
    print("     • 查找包含联系人占位符的模板")
    print("     • 使用正确的模板文件")
    
    print("\n   方案3: 创建新的模板")
    print("     • 基于现有模板创建包含联系人字段的新版本")
    print("     • 确保占位符格式正确")
    
    print("\n🎯 推荐方案:")
    print("   建议使用方案1，直接在现有模板中添加联系人占位符")
    print("   这样可以保持现有的模板结构和格式")

def main():
    """主分析函数"""
    print("🎯 Word模板内容详细分析")
    print("=" * 70)
    
    # 提取模板内容
    content, found_content = extract_full_template_content()
    
    # 搜索模板结构
    tables = search_template_structure()
    
    # 分析现有占位符
    placeholders = analyze_existing_placeholders()
    
    # 建议修复方案
    suggest_template_fixes()
    
    print("\n📊 分析总结")
    print("=" * 70)
    
    print("🔍 关键发现:")
    print("1. ❌ 模板中确实缺少 {{contactPerson}} 和 {{contactPhone}} 占位符")
    print("2. ✅ Java代码和Python代码都已经正确实现了联系人字段处理")
    print("3. ✅ 数据流转正常，问题出在模板层面")
    
    print("\n🎯 下一步行动:")
    print("1. 需要修改Word模板，添加联系人占位符")
    print("2. 或者查找是否有包含联系人字段的其他模板")
    print("3. 修复后重新测试联系人信息显示")

if __name__ == "__main__":
    main()
