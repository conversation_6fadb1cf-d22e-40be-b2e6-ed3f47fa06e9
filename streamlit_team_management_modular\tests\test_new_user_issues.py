#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新用户的Word生成问题
"""

import zipfile
import xml.etree.ElementTree as ET
import os
import json

def analyze_new_user_data():
    """分析新用户数据"""
    print("🔍 分析新用户数据")
    print("=" * 60)
    
    user_id = 'user_c61aa17e3868'
    
    # 查找用户数据
    user_data_path = f"data/{user_id}"
    if not os.path.exists(user_data_path):
        print(f"❌ 用户数据路径不存在: {user_data_path}")
        return None
    
    print(f"✅ 找到用户数据路径: {user_data_path}")
    
    # 查看用户数据结构
    print(f"\n📄 用户数据结构:")
    for item in os.listdir(user_data_path):
        item_path = os.path.join(user_data_path, item)
        if os.path.isdir(item_path):
            print(f"   📁 {item}/")
            # 查看子目录内容
            try:
                sub_items = os.listdir(item_path)
                for sub_item in sub_items[:5]:  # 只显示前5个
                    print(f"      📄 {sub_item}")
                if len(sub_items) > 5:
                    print(f"      ... 还有{len(sub_items)-5}个文件")
            except:
                pass
        else:
            print(f"   📄 {item}")
    
    # 查找团队数据
    teams_path = os.path.join(user_data_path, "teams")
    if not os.path.exists(teams_path):
        print(f"❌ 团队数据路径不存在: {teams_path}")
        return None
    
    team_files = [f for f in os.listdir(teams_path) if f.endswith('.json')]
    if not team_files:
        print(f"❌ 未找到团队数据文件")
        return None
    
    print(f"\n📄 找到团队文件:")
    for team_file in team_files:
        print(f"   • {team_file}")
    
    # 使用第一个团队文件
    team_file = team_files[0]
    team_name = team_file.replace('.json', '')
    
    print(f"\n📄 分析团队: {team_name}")
    
    # 加载团队数据
    with open(os.path.join(teams_path, team_file), 'r', encoding='utf-8') as f:
        team_data = json.load(f)
    
    print(f"📄 团队数据结构:")
    for key, value in team_data.items():
        if isinstance(value, list):
            print(f"   {key}: 列表 (长度: {len(value)})")
            if value and isinstance(value[0], dict):
                print(f"      第一个元素的键: {list(value[0].keys())}")
        elif isinstance(value, dict):
            print(f"   {key}: 字典 (键: {list(value.keys())})")
        else:
            print(f"   {key}: {type(value).__name__} = '{value}'")
    
    # 加载AI数据
    ai_data_path = os.path.join(user_data_path, "enhanced_ai_data", f"{team_name}_ai_data.json")
    ai_data = None
    if os.path.exists(ai_data_path):
        with open(ai_data_path, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        print(f"\n✅ 找到AI数据")
        
        # 分析AI数据结构
        print(f"📄 AI数据结构:")
        if 'extracted_info' in ai_data:
            extracted_info = ai_data['extracted_info']
            if 'basic_info' in extracted_info:
                basic_info = extracted_info['basic_info']
                print(f"   basic_info:")
                for key, value in basic_info.items():
                    print(f"     {key}: '{value}'")
            
            if 'additional_info' in extracted_info:
                additional_info = extracted_info['additional_info']
                print(f"   additional_info:")
                for key, value in additional_info.items():
                    print(f"     {key}: '{value}'")
    else:
        print(f"⚠️ 未找到AI数据: {ai_data_path}")
    
    return {
        'user_id': user_id,
        'team_name': team_name,
        'team_data': team_data,
        'ai_data': ai_data
    }

def test_word_generation_with_new_user(user_data):
    """使用新用户数据测试Word生成"""
    print(f"\n🔍 使用新用户数据测试Word生成")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = user_data['user_id']
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService(user_data['user_id'])
        
        # 测试Word生成
        print(f"📄 测试团队: {user_data['team_name']}")
        
        word_result = workflow_service._auto_generate_word_document(
            user_data['team_name'], {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            
            # 检查生成的文件内容
            file_path = word_result.get('file_path')
            if file_path and os.path.exists(file_path):
                return analyze_new_user_word_content(file_path, user_data)
            else:
                print("❌ 生成的文件不存在")
                return None
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_new_user_word_content(file_path, user_data):
    """分析新用户生成的Word内容"""
    print(f"\n🔍 分析新用户生成的Word内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 新用户Word文档完整内容:")
                    print(f"   {full_text}")
                    
                    # 分析问题
                    analysis_result = {}
                    
                    # 1. 检查团队名称
                    print(f"\n📋 1. 团队名称检查:")
                    team_name = user_data['team_name']
                    has_team_name = team_name in full_text
                    has_default_name = "足球队" in full_text and team_name not in full_text
                    
                    if has_team_name:
                        print(f"   ✅ 团队名称正确: '{team_name}'")
                        analysis_result['team_name'] = 'correct'
                    elif has_default_name:
                        print(f"   ❌ 显示默认名称: '足球队' (应该是'{team_name}')")
                        analysis_result['team_name'] = 'default'
                    else:
                        print(f"   ⚠️ 团队名称状态不明")
                        analysis_result['team_name'] = 'unknown'
                    
                    # 2. 检查"自动填充"问题
                    print(f"\n📋 2. '自动填充'问题检查:")
                    auto_fill_count = full_text.count("自动填充")
                    
                    if auto_fill_count == 0:
                        print(f"   ✅ 无'自动填充'问题")
                        analysis_result['auto_fill'] = 'fixed'
                    else:
                        print(f"   ❌ 发现{auto_fill_count}个'自动填充'")
                        analysis_result['auto_fill'] = 'problem'
                        
                        # 显示"自动填充"位置
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "自动填充" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+3)
                                context = ' '.join(words[start:end])
                                print(f"     发现: {context}")
                    
                    # 3. 检查联系人信息
                    print(f"\n📋 3. 联系人信息检查:")
                    if user_data['ai_data']:
                        extracted_info = user_data['ai_data'].get('extracted_info', {})
                        basic_info = extracted_info.get('basic_info', {})
                        
                        contact_person = basic_info.get('contact_person', '')
                        contact_phone = basic_info.get('contact_phone', '')
                        
                        has_contact_person = contact_person and contact_person in full_text
                        has_contact_phone = contact_phone and contact_phone in full_text
                        
                        print(f"   联系人'{contact_person}': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                        print(f"   联系电话'{contact_phone}': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                        
                        analysis_result['contact_info'] = has_contact_person and has_contact_phone
                    else:
                        print(f"   ⚠️ 无AI数据，无法检查联系人信息")
                        analysis_result['contact_info'] = None
                    
                    # 4. 检查球员信息
                    print(f"\n📋 4. 球员信息检查:")
                    players = user_data['team_data'].get('players', [])
                    if players:
                        player_count = len(players)
                        print(f"   团队数据中有{player_count}个球员")
                        
                        # 检查前几个球员
                        found_players = 0
                        for i, player in enumerate(players[:3]):
                            player_name = player.get('name', '')
                            if player_name and player_name in full_text:
                                print(f"   ✅ 球员{i+1} '{player_name}': 找到")
                                found_players += 1
                            else:
                                print(f"   ❌ 球员{i+1} '{player_name}': 未找到")
                        
                        analysis_result['players'] = found_players
                    else:
                        print(f"   ⚠️ 团队数据中无球员信息")
                        analysis_result['players'] = 0
                    
                    return analysis_result
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def compare_with_previous_user():
    """对比与之前用户的差异"""
    print(f"\n🔍 对比与之前用户的差异")
    print("=" * 60)
    
    # 对比两个用户的数据结构
    old_user = 'user_44ecbeed9db2'
    new_user = 'user_c61aa17e3868'
    
    print(f"📄 对比用户数据结构:")
    print(f"   旧用户: {old_user}")
    print(f"   新用户: {new_user}")
    
    # 检查数据路径
    old_path = f"data/{old_user}"
    new_path = f"data/{new_user}"
    
    print(f"\n📄 数据路径对比:")
    print(f"   旧用户路径存在: {os.path.exists(old_path)}")
    print(f"   新用户路径存在: {os.path.exists(new_path)}")
    
    if os.path.exists(old_path) and os.path.exists(new_path):
        old_structure = set(os.listdir(old_path))
        new_structure = set(os.listdir(new_path))
        
        print(f"\n📄 目录结构对比:")
        print(f"   共同目录: {old_structure & new_structure}")
        print(f"   旧用户独有: {old_structure - new_structure}")
        print(f"   新用户独有: {new_structure - old_structure}")

def generate_problem_report(analysis_result, user_data):
    """生成问题报告"""
    print(f"\n📊 新用户问题报告")
    print("=" * 70)
    
    if not analysis_result:
        print("❌ 无法生成报告，Word内容分析失败")
        return
    
    print(f"🎯 新用户 {user_data['user_id']} 的Word生成问题分析:")
    
    # 统计问题
    problems = []
    successes = []
    
    # 团队名称
    if analysis_result.get('team_name') == 'correct':
        successes.append("团队名称正确")
    elif analysis_result.get('team_name') == 'default':
        problems.append("团队名称显示为默认值")
    else:
        problems.append("团队名称状态不明")
    
    # 自动填充
    if analysis_result.get('auto_fill') == 'fixed':
        successes.append("无自动填充问题")
    else:
        problems.append("仍有自动填充问题")
    
    # 联系人信息
    if analysis_result.get('contact_info') is True:
        successes.append("联系人信息正常")
    elif analysis_result.get('contact_info') is False:
        problems.append("联系人信息有问题")
    else:
        problems.append("无法检查联系人信息")
    
    # 球员信息
    player_count = analysis_result.get('players', 0)
    if player_count > 0:
        successes.append(f"找到{player_count}个球员")
    else:
        problems.append("未找到球员信息")
    
    print(f"\n✅ 正常功能 ({len(successes)}个):")
    for success in successes:
        print(f"   • {success}")
    
    print(f"\n❌ 问题功能 ({len(problems)}个):")
    for problem in problems:
        print(f"   • {problem}")
    
    # 成功率
    total_checks = len(successes) + len(problems)
    success_rate = (len(successes) / total_checks * 100) if total_checks > 0 else 0
    
    print(f"\n📊 新用户修复成功率: {len(successes)}/{total_checks} ({success_rate:.1f}%)")
    
    # 结论
    if success_rate >= 75:
        print(f"\n🎉 新用户的修复基本成功！")
        print(f"💡 大部分功能正常工作")
    elif success_rate >= 50:
        print(f"\n⚠️ 新用户的修复部分成功")
        print(f"💡 仍有一些问题需要解决")
    else:
        print(f"\n❌ 新用户的修复效果不佳")
        print(f"💡 需要针对新用户数据进行进一步修复")
    
    # 修复建议
    if problems:
        print(f"\n💡 针对新用户的修复建议:")
        if "团队名称显示为默认值" in problems:
            print(f"   1. 检查新用户的团队数据结构")
            print(f"   2. 确认团队名称字段映射")
        if "仍有自动填充问题" in problems:
            print(f"   3. 检查新用户的AI数据质量")
            print(f"   4. 验证数据过滤逻辑")
        if "联系人信息有问题" in problems:
            print(f"   5. 检查新用户的AI数据结构")
        if "未找到球员信息" in problems:
            print(f"   6. 检查新用户的球员数据")

def main():
    """主函数"""
    print("🎯 测试新用户的Word生成问题")
    print("=" * 70)
    print("检查我们的修复在新用户数据上是否有效")
    print("=" * 70)
    
    # 1. 分析新用户数据
    user_data = analyze_new_user_data()
    
    if not user_data:
        print("❌ 无法获取新用户数据，测试终止")
        return
    
    # 2. 测试Word生成
    analysis_result = test_word_generation_with_new_user(user_data)
    
    # 3. 对比与之前用户的差异
    compare_with_previous_user()
    
    # 4. 生成问题报告
    generate_problem_report(analysis_result, user_data)

if __name__ == "__main__":
    main()
