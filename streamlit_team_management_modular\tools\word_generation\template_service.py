#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板管理服务
Template Management Service

统一管理AI换装模板图片，包括系统默认模板和用户自定义模板
"""

import os
import shutil
from typing import List, Dict, Optional, Any
from datetime import datetime
import streamlit as st

from config.settings import app_settings
from utils.helpers import ensure_directory_exists
from utils.image_utils import ImageProcessor


class TemplateService:
    """模板管理服务"""
    
    def __init__(self, user_id: str = None):
        self.user_id = user_id
        
        # 系统默认模板路径
        self.system_templates_folder = os.path.join("assets", "default_clothes")
        
        # 用户专属模板路径
        if user_id:
            from services.auth_service import AuthService
            auth_service = AuthService()
            user_data_path = auth_service.get_user_data_path(user_id)
            self.user_templates_folder = os.path.join(user_data_path, "templates")
            self.user_team_templates_folder = os.path.join(self.user_templates_folder, "team_templates")
            self.user_personal_templates_folder = os.path.join(self.user_templates_folder, "personal_templates")
        else:
            self.user_templates_folder = None
            self.user_team_templates_folder = None
            self.user_personal_templates_folder = None
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self) -> None:
        """确保所有必要的目录存在"""
        # 确保系统模板目录存在
        ensure_directory_exists(self.system_templates_folder)
        
        # 创建系统默认模板子目录
        system_subdirs = ["football_jerseys", "casual_wear", "formal_wear"]
        for subdir in system_subdirs:
            ensure_directory_exists(os.path.join(self.system_templates_folder, subdir))
        
        # 确保用户模板目录存在
        if self.user_templates_folder:
            ensure_directory_exists(self.user_templates_folder)
            ensure_directory_exists(self.user_team_templates_folder)
            ensure_directory_exists(self.user_personal_templates_folder)
    
    def get_system_templates(self) -> Dict[str, List[Dict[str, str]]]:
        """
        获取系统默认模板列表
        
        Returns:
            Dict[str, List[Dict[str, str]]]: 按类别分组的模板列表
        """
        templates = {
            "football_jerseys": [],
            "casual_wear": [],
            "formal_wear": []
        }
        
        try:
            for category in templates.keys():
                category_path = os.path.join(self.system_templates_folder, category)
                if os.path.exists(category_path):
                    for filename in os.listdir(category_path):
                        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                            templates[category].append({
                                "name": os.path.splitext(filename)[0],
                                "filename": filename,
                                "path": os.path.join(category_path, filename),
                                "category": category
                            })
        except Exception as e:
            st.error(f"获取系统模板失败: {e}")
        
        return templates
    
    def get_user_templates(self, team_name: str = None) -> Dict[str, List[Dict[str, str]]]:
        """
        获取用户模板列表
        
        Args:
            team_name: 球队名称，如果提供则包含球队专用模板
            
        Returns:
            Dict[str, List[Dict[str, str]]]: 用户模板列表
        """
        if not self.user_templates_folder:
            return {"personal": [], "team": []}
        
        templates = {"personal": [], "team": []}
        
        try:
            # 获取个人模板
            if os.path.exists(self.user_personal_templates_folder):
                for filename in os.listdir(self.user_personal_templates_folder):
                    if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                        templates["personal"].append({
                            "name": os.path.splitext(filename)[0],
                            "filename": filename,
                            "path": os.path.join(self.user_personal_templates_folder, filename),
                            "category": "personal"
                        })
            
            # 获取球队专用模板
            if team_name:
                team_template_path = os.path.join(self.user_team_templates_folder, team_name)
                if os.path.exists(team_template_path):
                    for filename in os.listdir(team_template_path):
                        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                            templates["team"].append({
                                "name": os.path.splitext(filename)[0],
                                "filename": filename,
                                "path": os.path.join(team_template_path, filename),
                                "category": "team"
                            })
        except Exception as e:
            st.error(f"获取用户模板失败: {e}")
        
        return templates
    
    def save_user_template(self, template_file, filename: str, team_name: str = None, 
                          category: str = "personal") -> Optional[str]:
        """
        保存用户上传的模板图片
        
        Args:
            template_file: 模板文件
            filename: 文件名
            team_name: 球队名称（如果是球队专用模板）
            category: 模板类别 ("personal" 或 "team")
            
        Returns:
            Optional[str]: 保存的文件路径，如果失败返回None
        """
        if not self.user_templates_folder:
            st.error("用户未登录，无法保存模板")
            return None
        
        try:
            # 确定保存路径
            if category == "team" and team_name:
                save_folder = os.path.join(self.user_team_templates_folder, team_name)
            else:
                save_folder = self.user_personal_templates_folder
            
            ensure_directory_exists(save_folder)
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            name, ext = os.path.splitext(filename)
            unique_filename = f"{name}_{timestamp}{ext}"
            file_path = os.path.join(save_folder, unique_filename)
            
            # 保存文件
            with open(file_path, "wb") as f:
                f.write(template_file.getvalue())
            
            st.success(f"✅ 模板保存成功: {unique_filename}")
            return file_path
            
        except Exception as e:
            st.error(f"保存模板失败: {e}")
            return None
    
    def delete_user_template(self, template_path: str) -> bool:
        """
        删除用户模板
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            bool: 是否删除成功
        """
        try:
            if os.path.exists(template_path) and template_path.startswith(self.user_templates_folder):
                os.remove(template_path)
                st.success("✅ 模板删除成功")
                return True
            else:
                st.error("模板文件不存在或无权限删除")
                return False
        except Exception as e:
            st.error(f"删除模板失败: {e}")
            return False
    
    def get_all_templates(self, team_name: str = None) -> Dict[str, Any]:
        """
        获取所有可用模板（系统 + 用户）
        
        Args:
            team_name: 球队名称
            
        Returns:
            Dict[str, Any]: 所有模板的分类列表
        """
        system_templates = self.get_system_templates()
        user_templates = self.get_user_templates(team_name)
        
        return {
            "system": system_templates,
            "user": user_templates
        }
    
    def copy_system_template_to_user(self, system_template_path: str, 
                                   team_name: str = None) -> Optional[str]:
        """
        将系统模板复制到用户文件夹
        
        Args:
            system_template_path: 系统模板路径
            team_name: 球队名称（如果是球队专用）
            
        Returns:
            Optional[str]: 复制后的文件路径
        """
        if not self.user_templates_folder:
            return None
        
        try:
            filename = os.path.basename(system_template_path)
            
            # 确定目标路径
            if team_name:
                target_folder = os.path.join(self.user_team_templates_folder, team_name)
            else:
                target_folder = self.user_personal_templates_folder
            
            ensure_directory_exists(target_folder)
            
            # 生成唯一文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            name, ext = os.path.splitext(filename)
            unique_filename = f"{name}_copy_{timestamp}{ext}"
            target_path = os.path.join(target_folder, unique_filename)
            
            # 复制文件
            shutil.copy2(system_template_path, target_path)
            
            st.success(f"✅ 模板复制成功: {unique_filename}")
            return target_path
            
        except Exception as e:
            st.error(f"复制模板失败: {e}")
            return None
