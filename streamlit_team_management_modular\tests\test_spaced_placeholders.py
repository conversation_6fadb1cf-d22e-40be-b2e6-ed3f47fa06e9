#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户使用空格格式的占位符：{{contact Person}} 和 {{contact Phone}}
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET

def test_spaced_placeholder_structure():
    """测试带空格的占位符结构"""
    print("🔍 测试1: 检查带空格的占位符结构")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return False
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 检查新的占位符格式:")
                
                # 检查带空格的联系人占位符
                has_contact_person_spaced = '{{contact Person}}' in content
                has_contact_phone_spaced = '{{contact Phone}}' in content
                
                # 检查原始格式（驼峰命名）
                has_contact_person_camel = '{{contactPerson}}' in content
                has_contact_phone_camel = '{{contactPhone}}' in content
                
                print(f"   {{{{contact Person}}}}: {'✅ 找到' if has_contact_person_spaced else '❌ 未找到'}")
                print(f"   {{{{contact Phone}}}}: {'✅ 找到' if has_contact_phone_spaced else '❌ 未找到'}")
                print(f"   {{{{contactPerson}}}}: {'✅ 找到' if has_contact_person_camel else '❌ 未找到'}")
                print(f"   {{{{contactPhone}}}}: {'✅ 找到' if has_contact_phone_camel else '❌ 未找到'}")
                
                # 检查是否还有分割的占位符
                has_split_placeholders = ('contact Person</w:t></w:r>' in content or 
                                        'contact Phone</w:t></w:r>' in content or
                                        'contactPerson</w:t></w:r>' in content or
                                        'contactPhone</w:t></w:r>' in content)
                
                print(f"   分割的占位符: {'⚠️ 仍存在' if has_split_placeholders else '✅ 已清理'}")
                
                # 显示联系人相关的上下文
                print(f"\n📄 联系人相关上下文:")
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if ('contact Person' in line or 'contact Phone' in line or 
                        'contactPerson' in line or 'contactPhone' in line):
                        print(f"   第{i+1}行: {line.strip()}")
                
                # 检查所有占位符
                import re
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                unique_placeholders = list(set(placeholders))
                
                print(f"\n📄 联系人相关占位符:")
                contact_placeholders = [p for p in unique_placeholders if 'contact' in p.lower()]
                for placeholder in sorted(contact_placeholders):
                    print(f"   • {placeholder}")
                
                return has_contact_person_spaced or has_contact_phone_spaced or has_contact_person_camel or has_contact_phone_camel
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_java_with_spaced_placeholders():
    """测试Java处理带空格的占位符"""
    print("\n🔍 测试2: Java处理带空格的占位符")
    print("=" * 60)
    
    try:
        # 创建测试数据 - 尝试两种字段名格式
        test_data_variants = [
            {
                "name": "驼峰命名格式",
                "data": {
                    "teamInfo": {
                        "title": "空格占位符测试报名表",
                        "organizationName": "空格占位符测试队",
                        "teamLeader": "张三",
                        "coach": "李四",
                        "teamDoctor": "王五",
                        "contactPerson": "赵六",
                        "contactPhone": "13800138000"
                    }
                }
            },
            {
                "name": "空格命名格式",
                "data": {
                    "teamInfo": {
                        "title": "空格占位符测试报名表",
                        "organizationName": "空格占位符测试队",
                        "teamLeader": "张三",
                        "coach": "李四",
                        "teamDoctor": "王五",
                        "contact Person": "赵六",
                        "contact Phone": "13800138000"
                    }
                }
            }
        ]
        
        java_dir = "../word_zc/ai-football-generator"
        results = []
        
        for variant in test_data_variants:
            print(f"\n📝 测试 {variant['name']}:")
            
            # 添加通用配置
            variant['data'].update({
                "players": [
                    {
                        "number": "10",
                        "name": "测试球员",
                        "photoPath": "java_word_photos/player1.png"
                    }
                ],
                "config": {
                    "templatePath": "template_15players_fixed.docx",
                    "outputDir": "output",
                    "photosDir": "java_word_photos"
                }
            })
            
            # 写入测试文件
            test_file = os.path.join(java_dir, f"test_spaced_{variant['name']}.json")
            with open(test_file, 'w', encoding='utf-8') as f:
                json.dump(variant['data'], f, ensure_ascii=False, indent=2)
            
            print(f"   测试数据: {list(variant['data']['teamInfo'].keys())}")
            
            # 运行Java程序
            result = subprocess.run(
                ["java", "-cp", "target/word-generator.jar", "CommandLineMain", f"test_spaced_{variant['name']}.json"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=30,
                errors='ignore',
                cwd=java_dir
            )
            
            print(f"   返回码: {result.returncode}")
            
            # 分析输出
            contact_parsed = False
            java_success = result.returncode == 0
            
            if result.stderr:
                for line in result.stderr.split('\n'):
                    if 'INFO:Team info parsed:' in line:
                        print(f"   关键日志: {line}")
                        if '联系人=赵六' in line and '联系电话=13800138000' in line:
                            print("   ✅ 联系人信息解析正确")
                            contact_parsed = True
                        elif '联系人=' in line and '联系电话=' in line:
                            print("   ⚠️ 联系人字段存在但值可能为空")
            
            # 检查生成的文件
            generated_file = None
            if java_success:
                output_dir = os.path.join(java_dir, "output")
                if os.path.exists(output_dir):
                    files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                    if files:
                        latest_file = max([os.path.join(output_dir, f) for f in files], 
                                        key=os.path.getmtime)
                        print(f"   生成文件: {os.path.basename(latest_file)}")
                        generated_file = latest_file
            
            results.append({
                'variant': variant['name'],
                'success': java_success,
                'contact_parsed': contact_parsed,
                'generated_file': generated_file
            })
            
            # 清理测试文件
            if os.path.exists(test_file):
                os.remove(test_file)
        
        return results
        
    except Exception as e:
        print(f"❌ Java测试失败: {e}")
        return []

def test_python_with_spaced_placeholders():
    """测试Python处理带空格的占位符"""
    print("\n🔍 测试3: Python处理带空格的占位符")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 测试数据
        team_data = {
            'name': 'Python空格占位符测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print(f"📄 Python测试数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("spaced_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 Python准备的JSON数据:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        print(f"   contact Person: '{team_info.get('contact Person', 'MISSING')}'")
        print(f"   contact Phone: '{team_info.get('contact Phone', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 运行Python生成...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            return {
                'success': True,
                'generated_file': result['file_path']
            }
        else:
            print(f"❌ 生成失败: {result['message']}")
            return {'success': False, 'generated_file': None}
            
    except Exception as e:
        print(f"❌ Python测试失败: {e}")
        return {'success': False, 'generated_file': None}

def check_word_content_for_spaced_placeholders(file_path, test_name):
    """检查Word内容中的空格占位符处理"""
    print(f"\n🔍 检查{test_name}生成的Word内容")
    print("=" * 60)
    
    if not file_path or not os.path.exists(file_path):
        print("❌ 文件不存在")
        return False
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    
                    # 检查各种占位符格式是否还存在
                    has_placeholder_camel_person = "{{contactPerson}}" in content
                    has_placeholder_camel_phone = "{{contactPhone}}" in content
                    has_placeholder_spaced_person = "{{contact Person}}" in content
                    has_placeholder_spaced_phone = "{{contact Phone}}" in content
                    
                    print(f"📄 内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_camel_person else '✅ 已替换'}")
                    print(f"   {{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_camel_phone else '✅ 已替换'}")
                    print(f"   {{{{contact Person}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_person else '✅ 已替换'}")
                    print(f"   {{{{contact Phone}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_phone else '✅ 已替换'}")
                    
                    # 显示联系人上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            break
                    
                    return {
                        'has_contact_person': has_contact_person,
                        'has_contact_phone': has_contact_phone,
                        'all_placeholders_replaced': not any([
                            has_placeholder_camel_person, has_placeholder_camel_phone,
                            has_placeholder_spaced_person, has_placeholder_spaced_phone
                        ])
                    }
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 测试带空格的占位符格式")
    print("=" * 70)
    print("用户使用了 {{contact Person}} 和 {{contact Phone}} 格式")
    print("=" * 70)
    
    # 测试1: 模板结构
    template_ok = test_spaced_placeholder_structure()
    
    # 测试2: Java生成（多种格式）
    java_results = test_java_with_spaced_placeholders()
    
    # 测试3: Python生成
    python_result = test_python_with_spaced_placeholders()
    
    # 检查生成的内容
    java_content_results = []
    for java_result in java_results:
        if java_result.get('generated_file'):
            content_result = check_word_content_for_spaced_placeholders(
                java_result['generated_file'], 
                f"Java({java_result['variant']})"
            )
            java_content_results.append({
                'variant': java_result['variant'],
                'content': content_result
            })
    
    python_content = None
    if python_result.get('generated_file'):
        python_content = check_word_content_for_spaced_placeholders(
            python_result['generated_file'], 
            "Python"
        )
    
    # 综合结果
    print(f"\n📊 测试结果汇总")
    print("=" * 70)
    
    print(f"🔍 模板结构: {'✅ 正常' if template_ok else '❌ 有问题'}")
    
    # Java结果
    for java_result in java_results:
        variant = java_result['variant']
        print(f"🔍 Java({variant}): {'✅ 正常' if java_result.get('success') else '❌ 有问题'}")
        print(f"🔍 Java({variant})联系人解析: {'✅ 正常' if java_result.get('contact_parsed') else '❌ 有问题'}")
    
    print(f"🔍 Python集成: {'✅ 正常' if python_result.get('success') else '❌ 有问题'}")
    
    # 内容检查结果
    for content_result in java_content_results:
        variant = content_result['variant']
        content = content_result['content']
        if content:
            contact_ok = content.get('has_contact_person') and content.get('has_contact_phone')
            print(f"🔍 Java({variant})生成内容: {'✅ 联系人信息正确' if contact_ok else '❌ 联系人信息缺失'}")
    
    if python_content:
        python_contact_ok = python_content.get('has_contact_person') and python_content.get('has_contact_phone')
        print(f"🔍 Python生成内容: {'✅ 联系人信息正确' if python_contact_ok else '❌ 联系人信息缺失'}")
    
    # 最终结论
    print(f"\n🎯 最终结论:")
    
    # 检查是否有任何成功的情况
    any_java_success = any(r.get('success') and r.get('contact_parsed') for r in java_results)
    any_java_content_success = any(
        c.get('content', {}).get('has_contact_person') and c.get('content', {}).get('has_contact_phone') 
        for c in java_content_results
    )
    
    python_success = python_result.get('success')
    python_content_success = (python_content and 
                            python_content.get('has_contact_person') and 
                            python_content.get('has_contact_phone'))
    
    if template_ok and any_java_success and any_java_content_success and python_success and python_content_success:
        print("🎉 空格占位符格式完全成功！")
        print("✅ 模板占位符格式正确（没有被XML分割）")
        print("✅ Java能够正确处理联系人信息")
        print("✅ Python能够正确处理联系人信息")
        print("✅ 生成的Word文档包含正确的联系人信息")
        print("✅ 没有发现其他报错")
        
        print(f"\n🎯 用户的空格占位符方案有效！")
        print(f"   ✅ {{{{contact Person}}}} 和 {{{{contact Phone}}}} 格式可以正常工作")
        print(f"   ✅ 避免了Word拼写检查的XML分割问题")
        print(f"   ✅ 联系人信息自动化流程完整可用")
        
    elif any_java_success or python_success:
        print("⚠️ 部分成功：")
        
        if any_java_success:
            successful_variants = [r['variant'] for r in java_results if r.get('success') and r.get('contact_parsed')]
            print(f"   ✅ Java程序在以下格式下成功: {', '.join(successful_variants)}")
        
        if python_success:
            print(f"   ✅ Python程序运行成功")
        
        if not any_java_content_success:
            print(f"   ❌ Java生成的Word文档联系人信息仍有问题")
        
        if not python_content_success:
            print(f"   ❌ Python生成的Word文档联系人信息仍有问题")
            
        print(f"\n💡 建议：需要调整Java代码以支持空格占位符格式")
        
    else:
        print("❌ 空格占位符格式仍有问题：")
        
        if not template_ok:
            print("   ❌ 模板结构仍有问题")
        if not any_java_success:
            print("   ❌ Java程序无法处理任何格式")
        if not python_success:
            print("   ❌ Python程序运行失败")

if __name__ == "__main__":
    main()
