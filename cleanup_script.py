#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码冗余清理脚本
自动移动测试文件和Word工具文件到对应目录
"""

import os
import shutil
import re
from pathlib import Path

class CodeCleanupManager:
    """代码清理管理器"""
    
    def __init__(self):
        self.base_dir = "streamlit_team_management_modular"
        self.tests_dir = os.path.join(self.base_dir, "tests")
        self.tools_dir = os.path.join(self.base_dir, "tools", "word_generation")
        
        # 确保目录存在
        os.makedirs(self.tests_dir, exist_ok=True)
        os.makedirs(self.tools_dir, exist_ok=True)
        
        self.moved_files = []
        self.errors = []
    
    def identify_test_files(self):
        """识别测试文件"""
        test_patterns = [
            r'^test_.*\.py$',
            r'.*_test\.py$',
            r'.*_debug\.py$',
            r'^comprehensive_.*_test\.py$',
            r'^final_.*_test\.py$'
        ]
        
        test_files = []
        
        for root, dirs, files in os.walk(self.base_dir):
            # 跳过已经在tests目录中的文件
            if 'tests' in root or 'tools' in root:
                continue
                
            for file in files:
                if file.endswith('.py'):
                    for pattern in test_patterns:
                        if re.match(pattern, file, re.IGNORECASE):
                            test_files.append(os.path.join(root, file))
                            break
        
        return test_files
    
    def identify_word_tools(self):
        """识别Word工具文件"""
        word_tool_patterns = [
            r'.*template.*\.py$',
            r'.*word.*\.py$',
            r'check_.*\.py$',
            r'analyze_.*\.py$',
            r'fix_.*\.py$',
            r'create_.*template.*\.py$',
            r'verify_.*\.py$'
        ]
        
        # 排除核心生产文件
        exclude_patterns = [
            r'word_generator_service\.py$',  # 核心服务
            r'fashion_workflow_service\.py$',  # 核心工作流
            r'settings\.py$',  # 配置文件
            r'app\.py$'  # 主应用
        ]
        
        word_tools = []
        
        for root, dirs, files in os.walk(self.base_dir):
            # 跳过已经在tests或tools目录中的文件
            if 'tests' in root or 'tools' in root:
                continue
                
            for file in files:
                if file.endswith('.py'):
                    # 检查是否是排除的核心文件
                    is_excluded = any(re.search(pattern, file) for pattern in exclude_patterns)
                    if is_excluded:
                        continue
                    
                    # 检查是否匹配Word工具模式
                    for pattern in word_tool_patterns:
                        if re.match(pattern, file, re.IGNORECASE):
                            file_path = os.path.join(root, file)
                            # 确保不是已经识别的测试文件
                            if not any(re.match(test_pattern, file, re.IGNORECASE) 
                                     for test_pattern in [r'^test_.*\.py$', r'.*_test\.py$', r'.*_debug\.py$']):
                                word_tools.append(file_path)
                            break
        
        return word_tools
    
    def move_files(self, files, target_dir, category_name):
        """移动文件到目标目录"""
        print(f"\n📁 移动{category_name}文件到 {target_dir}")
        print("=" * 60)
        
        moved_count = 0
        
        for file_path in files:
            try:
                filename = os.path.basename(file_path)
                target_path = os.path.join(target_dir, filename)
                
                # 检查目标文件是否已存在
                if os.path.exists(target_path):
                    print(f"⚠️  跳过 {filename} (目标文件已存在)")
                    continue
                
                # 移动文件
                shutil.move(file_path, target_path)
                self.moved_files.append({
                    'source': file_path,
                    'target': target_path,
                    'category': category_name
                })
                
                print(f"✅ 移动: {filename}")
                moved_count += 1
                
            except Exception as e:
                error_msg = f"移动 {file_path} 失败: {str(e)}"
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")
        
        print(f"\n📊 {category_name}移动统计: {moved_count}/{len(files)} 个文件")
        return moved_count
    
    def create_readme_files(self):
        """创建README文件说明目录用途"""
        
        # 创建tests目录README
        tests_readme = os.path.join(self.tests_dir, "README.md")
        tests_content = """# 测试文件目录

这个目录包含所有的测试、调试和验证文件。

## 文件类型
- `test_*.py` - 单元测试和集成测试
- `*_test.py` - 功能测试文件
- `*_debug.py` - 调试脚本
- `comprehensive_*_test.py` - 综合测试
- `final_*_test.py` - 最终验证测试

## 使用说明
这些文件用于开发和调试过程中的测试验证，不影响生产环境的核心功能。

## 注意事项
- 测试文件与生产代码分离，避免LLM混淆
- 运行测试前请确保环境配置正确
- 部分测试可能需要特定的数据文件或配置
"""
        
        with open(tests_readme, 'w', encoding='utf-8') as f:
            f.write(tests_content)
        
        # 创建word_generation目录README
        tools_readme = os.path.join(self.tools_dir, "README.md")
        tools_content = """# Word生成工具目录

这个目录包含Word文档生成相关的工具和辅助脚本。

## 文件类型
- `*template*.py` - 模板处理工具
- `check_*.py` - 检查和验证工具
- `analyze_*.py` - 分析工具
- `fix_*.py` - 修复工具
- `create_*.py` - 创建工具
- `verify_*.py` - 验证工具

## 核心生产文件位置
核心的Word生成功能文件仍在主目录中：
- `word_generator_service.py` - Word生成核心服务
- `services/fashion_workflow_service.py` - 工作流服务
- `config/settings.py` - 配置文件

## 使用说明
这些工具主要用于：
- Word模板的创建和修复
- 生成功能的调试和分析
- 模板格式的验证和检查

## 注意事项
- 工具文件与核心功能分离，提高代码清晰度
- 使用工具前请阅读具体文件的文档说明
- 部分工具可能需要特定的模板文件或配置
"""
        
        with open(tools_readme, 'w', encoding='utf-8') as f:
            f.write(tools_content)
        
        print("📝 已创建README文件说明目录用途")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print("\n" + "=" * 80)
        print("📊 代码清理完成报告")
        print("=" * 80)
        
        # 按类别统计
        by_category = {}
        for item in self.moved_files:
            category = item['category']
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(item)
        
        print(f"📈 清理统计:")
        total_moved = len(self.moved_files)
        print(f"   总移动文件: {total_moved} 个")
        
        for category, files in by_category.items():
            print(f"   {category}: {len(files)} 个")
        
        if self.errors:
            print(f"\n❌ 错误: {len(self.errors)} 个")
            for error in self.errors[:5]:  # 只显示前5个错误
                print(f"   • {error}")
            if len(self.errors) > 5:
                print(f"   ... 还有 {len(self.errors) - 5} 个错误")
        
        print(f"\n📁 新目录结构:")
        print(f"   📂 {self.tests_dir}/ - 测试文件")
        print(f"   📂 {self.tools_dir}/ - Word工具")
        
        # 保存详细报告
        report_file = "cleanup_report.json"
        import json
        from datetime import datetime
        report_data = {
            'timestamp': str(datetime.now()),
            'moved_files': self.moved_files,
            'errors': self.errors,
            'statistics': {
                'total_moved': total_moved,
                'by_category': {cat: len(files) for cat, files in by_category.items()},
                'error_count': len(self.errors)
            }
        }
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"\n💾 详细报告已保存到: {report_file}")

def main():
    """主函数"""
    from datetime import datetime
    
    print("🚀 开始代码冗余清理")
    print(f"清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    cleaner = CodeCleanupManager()
    
    # 识别文件
    print("\n🔍 识别需要移动的文件...")
    test_files = cleaner.identify_test_files()
    word_tools = cleaner.identify_word_tools()
    
    print(f"📋 识别结果:")
    print(f"   测试文件: {len(test_files)} 个")
    print(f"   Word工具: {len(word_tools)} 个")
    
    # 移动文件
    if test_files:
        cleaner.move_files(test_files, cleaner.tests_dir, "测试")
    
    if word_tools:
        cleaner.move_files(word_tools, cleaner.tools_dir, "Word工具")
    
    # 创建说明文件
    cleaner.create_readme_files()
    
    # 生成报告
    cleaner.generate_cleanup_report()
    
    print("\n✅ 代码清理完成")

if __name__ == "__main__":
    main()
