#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解决方案实现和测试
Solution Implementation and Testing
"""

import os
import json
from pathlib import Path

def test_ai_data_loading_logic():
    """测试AI数据加载逻辑"""
    print("🔍 测试AI数据加载逻辑")
    print("=" * 40)
    
    user_id = "37d53472d725"
    team_name = "003222"
    
    # 模拟_load_ai_export_data方法的逻辑
    print("方法1: 检查AI聊天数据")
    ai_chat_data_path = f'data/user_{user_id}/enhanced_ai_data/{team_name}_ai_data.json'
    
    if os.path.exists(ai_chat_data_path):
        print(f"✅ AI聊天数据存在: {ai_chat_data_path}")
        
        with open(ai_chat_data_path, 'r', encoding='utf-8') as f:
            ai_chat_data = json.load(f)
        
        if 'extracted_info' in ai_chat_data:
            print("✅ 包含extracted_info，需要转换格式")
            
            # 模拟转换逻辑
            team_file = f"data/user_{user_id}/teams/{team_name}.json"
            if os.path.exists(team_file):
                with open(team_file, 'r', encoding='utf-8') as f:
                    team_data = json.load(f)
                
                players = []
                for player in team_data.get('players', []):
                    # 构建照片信息
                    photo_info = {
                        "exists": bool(player.get('photo')),
                        "filename": player.get('photo', ''),
                        "absolute_path": ""
                    }
                    
                    # 构建照片绝对路径
                    if player.get('photo'):
                        photo_path = f"data/user_{user_id}/photos/{team_name}/{player['photo']}"
                        if os.path.exists(photo_path):
                            photo_info["absolute_path"] = os.path.abspath(photo_path)
                            print(f"   球员 {player.get('name', 'N/A')}: 照片路径 ✅")
                        else:
                            print(f"   球员 {player.get('name', 'N/A')}: 照片路径 ❌ {photo_path}")
                    
                    players.append({
                        "id": player.get('id', ''),
                        "name": player.get('name', ''),
                        "jersey_number": player.get('jersey_number', ''),
                        "photo_info": photo_info
                    })
                
                # 构建导出格式
                export_data = {
                    "team_info": {
                        "name": team_name,
                        "display_name": team_name,
                        "ai_extracted_info": ai_chat_data.get('extracted_info', {}),
                        "created_at": ai_chat_data.get('created_at', ''),
                        "updated_at": ai_chat_data.get('updated_at', '')
                    },
                    "players": players,
                    "export_time": ai_chat_data.get('updated_at', ''),
                    "data_source": "ai_chat_component"
                }
                
                print(f"✅ 转换后的导出数据包含 {len(players)} 名球员")
                
                # 检查准备状态
                players_with_photos = []
                for player in players:
                    photo_info = player.get("photo_info", {})
                    if photo_info.get("exists", False) and photo_info.get("absolute_path"):
                        players_with_photos.append(player)
                
                ready_players = len(players_with_photos)
                total_players = len(players)
                
                print(f"有照片的球员: {ready_players}/{total_players}")
                
                if ready_players > 0:
                    print("✅ 准备状态: 就绪")
                    return True, export_data
                else:
                    print("❌ 准备状态: 未就绪 (no_photos)")
                    return False, export_data
    
    print("方法2: 检查传统AI导出文件")
    ai_export_path = f"data/user_{user_id}/exports/team_{team_name}_ai_ready.json"
    
    if os.path.exists(ai_export_path):
        print(f"✅ 传统AI导出数据存在: {ai_export_path}")
        with open(ai_export_path, 'r', encoding='utf-8') as f:
            export_data = json.load(f)
        return True, export_data
    
    print("❌ 没有找到任何AI数据")
    return False, None

def analyze_execution_path():
    """分析执行路径问题"""
    print("\n🔍 分析执行路径问题")
    print("=" * 40)
    
    # 从工作流结果看，使用的是unified模式
    print("当前执行模式: unified")
    print("unified模式的判断逻辑:")
    print("   if readiness.get('ready', False):")
    print("       → _execute_ai_based_workflow (有自动Word生成)")
    print("   else:")
    print("       → _execute_manual_based_workflow (没有自动Word生成)")
    
    # 测试readiness状态
    ready, export_data = test_ai_data_loading_logic()
    
    if ready:
        print("\n✅ readiness应该返回True")
        print("   → 应该执行AI模式")
        print("   → 应该有自动Word生成")
        print("   → 但实际没有生成Word")
        print("\n🎯 问题可能在于:")
        print("1. AI数据加载逻辑有bug")
        print("2. 照片路径解析有问题")
        print("3. 执行路径判断有误")
    else:
        print("\n❌ readiness返回False")
        print("   → 执行手动模式")
        print("   → 没有自动Word生成")
        print("   → 这解释了为什么没有生成Word")

def propose_solutions():
    """提出解决方案"""
    print("\n💡 解决方案")
    print("=" * 40)
    
    print("方案1: 修复AI数据加载逻辑 (推荐)")
    print("   - 确保照片路径正确解析")
    print("   - 修复_convert_ai_chat_data_to_export_format方法")
    print("   - 让readiness检查返回正确状态")
    
    print("\n方案2: 在手动模式中添加自动Word生成")
    print("   - 修改_execute_manual_based_workflow方法")
    print("   - 添加自动Word生成逻辑")
    print("   - 确保所有执行路径都有Word生成")
    
    print("\n方案3: 统一Word生成逻辑")
    print("   - 将Word生成逻辑提取为独立方法")
    print("   - 在所有成功的换装流程后调用")
    print("   - 不依赖特定的执行路径")

def test_photo_path_fix():
    """测试照片路径修复"""
    print("\n🔧 测试照片路径修复")
    print("=" * 40)
    
    user_id = "37d53472d725"
    team_name = "003222"
    
    # 检查当前的照片路径逻辑
    team_file = f"data/user_{user_id}/teams/{team_name}.json"
    with open(team_file, 'r', encoding='utf-8') as f:
        team_data = json.load(f)
    
    for player in team_data.get('players', []):
        name = player.get('name', 'N/A')
        photo = player.get('photo', '')
        
        if photo:
            # 当前逻辑使用的路径
            current_path = f"data/user_{user_id}/photos/{photo}"
            # 正确的路径应该是
            correct_path = f"data/user_{user_id}/photos/{team_name}/{photo}"
            
            print(f"球员 {name}:")
            print(f"   当前逻辑路径: {current_path} {'✅' if os.path.exists(current_path) else '❌'}")
            print(f"   正确路径: {correct_path} {'✅' if os.path.exists(correct_path) else '❌'}")

def create_quick_fix():
    """创建快速修复方案"""
    print("\n🚀 创建快速修复方案")
    print("=" * 40)
    
    print("快速修复: 在手动模式中添加Word生成")
    print("修改文件: components/fashion_workflow.py")
    print("方法: _execute_manual_based_workflow")
    print("位置: 第715行附近")
    
    fix_code = '''
# 在换装成功后添加以下代码:
if fashion_result.get("success", False) and fashion_result.get("successful_count", 0) > 0:
    st.info("📄 开始自动生成Word报名表...")
    try:
        # 获取AI数据用于Word生成
        ai_data = self.workflow_service._load_ai_export_data(team_name)
        if ai_data:
            team_info = ai_data.get("team_info", {})
            ai_extracted_info = team_info.get("ai_extracted_info", {})
            basic_info = ai_extracted_info.get("basic_info", {})
            
            team_data = {
                "name": team_name,
                "leader": basic_info.get("leader_name", ""),
                "coach": basic_info.get("contact_person", ""),
                "doctor": basic_info.get("team_doctor", "")
            }
        else:
            team_data = {"name": team_name}
        
        word_result = self.workflow_service._auto_generate_word_document(
            team_name, player_photo_mapping, None
        )
        workflow_result["word_generation_result"] = word_result
        
        if word_result.get("success", False):
            st.success("✅ Word报名表自动生成成功！")
        else:
            st.warning(f"⚠️ Word生成失败: {word_result.get('error', '未知错误')}")
            
    except Exception as e:
        st.warning(f"⚠️ Word自动生成失败: {str(e)}")
'''
    
    print("修复代码:")
    print(fix_code)

def main():
    """主函数"""
    print("🚀 Word生成问题解决方案测试")
    print("=" * 50)
    
    # 测试AI数据加载
    test_ai_data_loading_logic()
    
    # 分析执行路径
    analyze_execution_path()
    
    # 测试照片路径修复
    test_photo_path_fix()
    
    # 提出解决方案
    propose_solutions()
    
    # 创建快速修复
    create_quick_fix()
    
    print("\n📊 总结")
    print("=" * 50)
    print("问题根因: AI数据加载逻辑中的照片路径解析错误")
    print("导致结果: readiness检查失败，走了手动模式，没有自动Word生成")
    print("最佳方案: 修复照片路径逻辑 + 在手动模式中添加Word生成")
    print("预期效果: 换装成功后自动生成Word文档")

if __name__ == "__main__":
    main()
