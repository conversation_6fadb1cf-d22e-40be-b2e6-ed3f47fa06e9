#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队徽占位符格式
验证 {{@teamLogoPhoto}} 是否是正确的poi-tl图片占位符格式
"""

import os
import zipfile
import re
import json
import subprocess
import tempfile
from datetime import datetime

class TeamLogoPlaceholderTester:
    """队徽占位符测试器"""
    
    def __init__(self):
        self.template_files = [
            "word_zc/template_15players_fixed.docx",
            "word_zc/template_15players.docx",
            "word_zc/template_15players_clean.docx"
        ]
        self.jar_path = "word_zc/ai-football-generator/target/word-generator.jar"
        
    def extract_placeholders_from_template(self, template_path):
        """从模板中提取所有占位符"""
        try:
            with zipfile.ZipFile(template_path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
                
                # 查找所有占位符
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                
                # 分类占位符
                image_placeholders = []
                text_placeholders = []
                other_placeholders = []
                
                for placeholder in placeholders:
                    if '@' in placeholder:
                        image_placeholders.append(placeholder)
                    elif any(symbol in placeholder for symbol in ['#', '*', '+', '?', '/']):
                        other_placeholders.append(placeholder)
                    else:
                        text_placeholders.append(placeholder)
                
                return {
                    'all_placeholders': list(set(placeholders)),
                    'image_placeholders': list(set(image_placeholders)),
                    'text_placeholders': list(set(text_placeholders)),
                    'other_placeholders': list(set(other_placeholders)),
                    'total_count': len(set(placeholders))
                }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_poi_tl_syntax(self):
        """分析poi-tl的语法规则"""
        print("=" * 80)
        print("📚 poi-tl图片占位符语法分析")
        print("=" * 80)
        
        print("根据poi-tl官方文档:")
        print("✅ 图片标签以 '@' 开头")
        print("✅ 格式: {{@variableName}}")
        print("✅ 示例: {{@logo}}, {{@watermelon}}, {{@lemon}}")
        print()
        
        print("🎯 测试目标占位符: {{@teamLogoPhoto}}")
        print("   - 符合 '@' 开头规则: ✅")
        print("   - 变量名 'teamLogoPhoto': ✅ (符合驼峰命名)")
        print("   - 整体格式: ✅ 符合poi-tl规范")
        print()
    
    def check_templates_for_logo_placeholder(self):
        """检查模板中是否存在队徽相关占位符"""
        print("=" * 80)
        print("🔍 检查模板中的队徽相关占位符")
        print("=" * 80)
        
        logo_related_patterns = [
            r'\{\{@.*[Ll]ogo.*\}\}',
            r'\{\{@.*[Tt]eam.*\}\}',
            r'\{\{@.*[Pp]hoto.*\}\}',
            r'\{\{@teamLogoPhoto\}\}'
        ]
        
        for template_file in self.template_files:
            if not os.path.exists(template_file):
                continue
                
            print(f"📄 检查模板: {os.path.basename(template_file)}")
            
            placeholder_info = self.extract_placeholders_from_template(template_file)
            
            if 'error' in placeholder_info:
                print(f"   ❌ 错误: {placeholder_info['error']}")
                continue
            
            # 检查图片占位符
            image_placeholders = placeholder_info['image_placeholders']
            print(f"   📸 图片占位符数量: {len(image_placeholders)}")
            
            if image_placeholders:
                print("   图片占位符列表:")
                for placeholder in sorted(image_placeholders):
                    print(f"     • {placeholder}")
            
            # 检查是否有队徽相关占位符
            logo_placeholders = []
            for pattern in logo_related_patterns:
                for placeholder in placeholder_info['all_placeholders']:
                    if re.match(pattern, placeholder):
                        logo_placeholders.append(placeholder)
            
            if logo_placeholders:
                print(f"   🏆 发现队徽相关占位符:")
                for placeholder in set(logo_placeholders):
                    print(f"     • {placeholder}")
            else:
                print("   ⚠️  未发现队徽相关占位符")
            
            print()
    
    def test_team_logo_placeholder_functionality(self):
        """测试队徽占位符的实际功能"""
        print("=" * 80)
        print("🧪 测试队徽占位符功能")
        print("=" * 80)
        
        # 创建测试图片
        test_logo_path = self.create_test_logo()
        
        # 测试数据
        test_data = {
            "teamInfo": {
                "title": "队徽占位符测试",
                "organizationName": "测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五"
            },
            "players": [
                {
                    "number": "10",
                    "name": "测试球员",
                    "photoPath": "word_zc/ai-football-generator/java_word_photos/player1.png"
                }
            ],
            # 测试不同的队徽字段名
            "teamLogoPhoto": test_logo_path,
            "teamLogo": test_logo_path,
            "logo": test_logo_path
        }
        
        # 测试主模板
        template_path = "word_zc/template_15players_fixed.docx"
        if os.path.exists(template_path):
            result = self.test_with_logo_data(template_path, test_data, "main_template")
            return result
        else:
            print("❌ 主模板文件不存在")
            return None
    
    def create_test_logo(self):
        """创建测试队徽图片"""
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # 创建一个简单的测试队徽
            img = Image.new('RGB', (200, 200), color='white')
            draw = ImageDraw.Draw(img)
            
            # 画一个圆形背景
            draw.ellipse([20, 20, 180, 180], fill='blue', outline='navy', width=3)
            
            # 添加文字
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
            
            draw.text((100, 100), "TEST\nLOGO", fill='white', font=font, anchor="mm")
            
            # 保存测试图片
            test_logo_path = "temp_test_logo.png"
            img.save(test_logo_path)
            print(f"✅ 创建测试队徽: {test_logo_path}")
            return os.path.abspath(test_logo_path)
            
        except ImportError:
            print("⚠️  PIL库不可用，使用现有图片作为测试")
            # 使用现有的图片文件
            existing_images = [
                "word_zc/ai-football-generator/java_word_photos/player1.png",
                "word_zc/ai-football-generator/java_word_photos/player2.jpg"
            ]
            for img_path in existing_images:
                if os.path.exists(img_path):
                    return os.path.abspath(img_path)
            return None
    
    def test_with_logo_data(self, template_path, test_data, test_name):
        """使用队徽数据测试模板"""
        print(f"🧪 测试: {test_name}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            try:
                # 准备配置
                test_data["config"] = {
                    "templatePath": os.path.abspath(template_path),
                    "outputDir": temp_dir,
                    "photosDir": "word_zc/ai-football-generator/java_word_photos"
                }
                
                # 写入测试JSON
                test_json_path = os.path.join(temp_dir, f"test_{test_name}.json")
                with open(test_json_path, 'w', encoding='utf-8') as f:
                    json.dump(test_data, f, ensure_ascii=False, indent=2)
                
                print(f"   📝 测试数据: {test_json_path}")
                
                # 执行Java程序
                cmd = [
                    "java", "-jar", os.path.abspath(self.jar_path),
                    test_json_path
                ]
                
                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=30,
                    cwd=os.getcwd()
                )
                
                # 检查结果
                if result.returncode == 0:
                    output_files = [f for f in os.listdir(temp_dir) if f.endswith('.docx')]
                    print(f"   ✅ 生成成功，输出文件: {len(output_files)} 个")
                    
                    if output_files:
                        output_file = os.path.join(temp_dir, output_files[0])
                        file_size = os.path.getsize(output_file)
                        print(f"      📄 {output_files[0]} ({file_size:,} 字节)")
                        
                        # 检查生成的Word文档内容
                        self.check_generated_word_for_logo(output_file)
                    
                    return {"success": True, "output_files": output_files}
                else:
                    print(f"   ❌ 生成失败: {result.stderr}")
                    return {"success": False, "error": result.stderr}
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {str(e)}")
                return {"success": False, "error": str(e)}
    
    def check_generated_word_for_logo(self, word_file_path):
        """检查生成的Word文档中是否包含队徽"""
        try:
            with zipfile.ZipFile(word_file_path, 'r') as zip_file:
                # 检查是否有图片文件
                image_files = [f for f in zip_file.namelist() if f.startswith('word/media/')]
                
                print(f"      📸 文档中的图片文件: {len(image_files)} 个")
                
                if image_files:
                    for img_file in image_files:
                        print(f"        • {img_file}")
                
                # 检查document.xml中的图片引用
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
                    
                    # 查找图片引用
                    image_refs = re.findall(r'<a:blip[^>]*r:embed="([^"]*)"', content)
                    print(f"      🔗 图片引用: {len(image_refs)} 个")
                    
                    # 检查是否还有未替换的占位符
                    remaining_placeholders = re.findall(r'\{\{@[^}]+\}\}', content)
                    if remaining_placeholders:
                        print(f"      ⚠️  未替换的图片占位符: {remaining_placeholders}")
                    else:
                        print(f"      ✅ 所有图片占位符已处理")
                        
        except Exception as e:
            print(f"      ❌ 检查Word文档失败: {e}")
    
    def cleanup_test_files(self):
        """清理测试文件"""
        test_files = ["temp_test_logo.png"]
        for file_path in test_files:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🗑️  清理测试文件: {file_path}")

def main():
    """主函数"""
    print("🚀 队徽占位符测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tester = TeamLogoPlaceholderTester()
    
    try:
        # 1. 分析poi-tl语法
        tester.analyze_poi_tl_syntax()
        
        # 2. 检查模板中的占位符
        tester.check_templates_for_logo_placeholder()
        
        # 3. 功能测试
        result = tester.test_team_logo_placeholder_functionality()
        
        # 4. 总结
        print("=" * 80)
        print("📊 测试总结")
        print("=" * 80)
        
        print("🎯 关于 {{@teamLogoPhoto}} 占位符:")
        print("   ✅ 语法正确: 符合poi-tl的 '@' 开头图片占位符规范")
        print("   ✅ 命名规范: 使用驼峰命名法，语义清晰")
        print("   ✅ 格式标准: {{@variableName}} 格式完全正确")
        
        if result and result.get('success'):
            print("   ✅ 功能验证: 可以正常处理图片数据")
        else:
            print("   ⚠️  功能验证: 需要检查数据映射和模板配置")
        
        print("\n💡 结论:")
        print("   {{@teamLogoPhoto}} 是正确的poi-tl图片占位符格式")
        print("   可以用于在Word模板中插入AI生成的队徽图片")
        
    finally:
        # 清理测试文件
        tester.cleanup_test_files()
    
    print("\n✅ 测试完成")

if __name__ == "__main__":
    main()
