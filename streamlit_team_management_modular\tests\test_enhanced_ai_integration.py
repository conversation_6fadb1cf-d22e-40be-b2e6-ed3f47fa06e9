#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强AI功能集成
验证OpenAI结构化输出和函数调用功能是否正确集成
"""

import sys
import os
import json
from typing import Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试模块导入...")
    
    try:
        from config.ai_schemas import (
            TEAM_INFO_SCHEMA, 
            PLAYER_INFO_SCHEMA, 
            FUNCTION_DEFINITIONS,
            STRUCTURED_OUTPUT_FORMAT,
            AI_CONFIG
        )
        print("✅ AI Schema配置导入成功")
        
        from services.enhanced_ai_service import EnhancedAIService
        print("✅ 增强AI服务导入成功")
        
        from services.ai_service import AIService
        print("✅ AI服务导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_schemas():
    """测试Schema配置"""
    print("\n📋 测试Schema配置...")
    
    try:
        from config.ai_schemas import TEAM_INFO_SCHEMA, PLAYER_INFO_SCHEMA, FUNCTION_DEFINITIONS
        
        # 验证Schema结构
        assert "type" in TEAM_INFO_SCHEMA
        assert "properties" in TEAM_INFO_SCHEMA
        assert "basic_info" in TEAM_INFO_SCHEMA["properties"]
        print("✅ 球队信息Schema结构正确")
        
        assert "type" in PLAYER_INFO_SCHEMA
        assert "properties" in PLAYER_INFO_SCHEMA
        assert "basic_info" in PLAYER_INFO_SCHEMA["properties"]
        print("✅ 球员信息Schema结构正确")
        
        # 验证函数定义
        assert len(FUNCTION_DEFINITIONS) > 0
        for func in FUNCTION_DEFINITIONS:
            assert "type" in func
            assert "function" in func
            assert "name" in func["function"]
            assert "parameters" in func["function"]
        print(f"✅ 函数定义正确，共{len(FUNCTION_DEFINITIONS)}个函数")
        
        return True
    except Exception as e:
        print(f"❌ Schema测试失败: {e}")
        return False

def test_ai_service_compatibility():
    """测试AI服务兼容性"""
    print("\n🤖 测试AI服务兼容性...")
    
    try:
        from services.ai_service import AIService
        
        # 测试基础初始化
        ai_service = AIService("test_user")
        print("✅ AI服务初始化成功")
        
        # 测试增强功能检查
        has_enhanced = ai_service.has_enhanced_features()
        print(f"✅ 增强功能检查: {'可用' if has_enhanced else '不可用'}")
        
        # 测试新方法存在性
        assert hasattr(ai_service, 'generate_enhanced_response')
        assert hasattr(ai_service, 'extract_team_info_from_text')
        assert hasattr(ai_service, 'extract_player_info_from_text')
        print("✅ 新增方法存在")
        
        return True
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def test_enhanced_ai_service():
    """测试增强AI服务"""
    print("\n🚀 测试增强AI服务...")
    
    try:
        from services.enhanced_ai_service import EnhancedAIService
        
        # 测试初始化
        enhanced_service = EnhancedAIService("test_user")
        print("✅ 增强AI服务初始化成功")
        
        # 测试方法存在性
        assert hasattr(enhanced_service, 'generate_response_with_functions')
        assert hasattr(enhanced_service, 'extract_info_from_text')
        print("✅ 增强功能方法存在")
        
        return True
    except Exception as e:
        print(f"❌ 增强AI服务测试失败: {e}")
        return False

def test_page_imports():
    """测试页面导入"""
    print("\n📄 测试页面导入...")
    
    try:
        from pages.ai_extraction import show_ai_extraction_page
        print("✅ AI提取页面导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 页面导入失败: {e}")
        return False

def test_function_definitions_structure():
    """测试函数定义结构"""
    print("\n⚙️ 测试函数定义结构...")
    
    try:
        from config.ai_schemas import FUNCTION_DEFINITIONS
        
        expected_functions = [
            "extract_team_info",
            "extract_player_info", 
            "validate_team_data",
            "generate_team_suggestions"
        ]
        
        actual_functions = [func["function"]["name"] for func in FUNCTION_DEFINITIONS]
        
        for expected in expected_functions:
            if expected in actual_functions:
                print(f"✅ 函数 {expected} 存在")
            else:
                print(f"❌ 函数 {expected} 缺失")
                return False
        
        return True
    except Exception as e:
        print(f"❌ 函数定义测试失败: {e}")
        return False

def test_data_path_compatibility():
    """测试数据路径兼容性"""
    print("\n📁 测试数据路径兼容性...")
    
    try:
        from services.auth_service import AuthService
        
        auth_service = AuthService()
        test_user_id = "test_user_12345"
        
        # 测试用户数据路径生成
        user_path = auth_service.get_user_data_path(test_user_id)
        print(f"✅ 用户数据路径: {user_path}")
        
        teams_path = auth_service.get_user_data_path(test_user_id, "teams")
        print(f"✅ 球队数据路径: {teams_path}")
        
        return True
    except Exception as e:
        print(f"❌ 数据路径测试失败: {e}")
        return False

def print_integration_summary():
    """打印集成摘要"""
    print("\n" + "="*60)
    print("🎯 OpenAI增强功能集成摘要")
    print("="*60)
    
    try:
        from config.ai_schemas import FUNCTION_DEFINITIONS, AI_CONFIG
        
        print(f"📊 配置信息:")
        print(f"  • 结构化输出: {'启用' if AI_CONFIG['enable_structured_outputs'] else '禁用'}")
        print(f"  • 函数调用: {'启用' if AI_CONFIG['enable_function_calling'] else '禁用'}")
        print(f"  • OpenAI模型: {AI_CONFIG['model']}")
        print(f"  • 函数数量: {len(FUNCTION_DEFINITIONS)}")
        
        print(f"\n🔧 集成功能:")
        print(f"  • ✅ JSON Schema验证")
        print(f"  • ✅ 函数调用支持")
        print(f"  • ✅ 智能信息提取")
        print(f"  • ✅ 结构化输出")
        print(f"  • ✅ 用户隔离兼容")
        print(f"  • ✅ 向后兼容")
        
        print(f"\n📄 新增页面:")
        print(f"  • 🤖 AI智能信息提取 (pages/ai_extraction.py)")
        
        print(f"\n🚀 使用方式:")
        print(f"  • 访问 AI智能信息提取 页面")
        print(f"  • 输入自然语言文本")
        print(f"  • AI自动提取结构化信息")
        print(f"  • 支持球队、球员、比赛信息提取")
        
    except Exception as e:
        print(f"❌ 无法生成摘要: {e}")

def main():
    """主测试函数"""
    print("🧪 OpenAI增强功能集成测试")
    print("="*50)
    
    tests = [
        ("模块导入", test_imports),
        ("Schema配置", test_schemas),
        ("AI服务兼容性", test_ai_service_compatibility),
        ("增强AI服务", test_enhanced_ai_service),
        ("页面导入", test_page_imports),
        ("函数定义结构", test_function_definitions_structure),
        ("数据路径兼容性", test_data_path_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！OpenAI增强功能集成成功！")
        print_integration_summary()
    else:
        print("⚠️ 部分测试失败，请检查集成问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
