#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有模板文件中的"自动填充"问题
"""

import os
import sys
import zipfile
from datetime import datetime

def check_template_file(template_path):
    """检查单个模板文件"""
    print(f"\n📄 检查模板: {os.path.basename(template_path)}")
    print("-" * 50)
    
    if not os.path.exists(template_path):
        print(f"❌ 文件不存在")
        return False
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        # 查找"自动填充"字符串
        auto_fill_count = content.count("自动填充")
        
        if auto_fill_count > 0:
            print(f"❌ 发现 {auto_fill_count} 个'自动填充'字符串")
            
            # 查找上下文
            import re
            pattern = r'.{0,30}自动填充.{0,30}'
            matches = re.findall(pattern, content)
            
            print(f"📄 '自动填充'出现的位置:")
            for i, match in enumerate(matches[:3]):
                clean_match = match.replace('<', '&lt;').replace('>', '&gt;')
                print(f"   {i+1}. {clean_match}")
            
            return False
        else:
            print(f"✅ 未发现'自动填充'字符串")
            
            # 检查颜色相关的占位符
            color_placeholders = [
                "{{jerseyColor}}", "{{shortsColor}}", "{{socksColor}}", "{{goalkeeperKitColor}}",
                "jerseyColor", "shortsColor", "socksColor", "goalkeeperKitColor"
            ]
            
            placeholders_found = []
            for placeholder in color_placeholders:
                if placeholder in content:
                    placeholders_found.append(placeholder)
            
            if placeholders_found:
                print(f"🎨 找到颜色占位符: {placeholders_found}")
            else:
                print(f"⚠️ 未找到颜色占位符")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_current_settings():
    """检查当前配置使用的模板"""
    print(f"\n📋 检查当前配置")
    print("-" * 50)
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from config.settings import app_settings
        
        # 获取当前配置的模板路径
        paths = app_settings.word_generator.get_absolute_paths("settings_check", app_settings.paths)
        current_template = paths['template_path']
        
        print(f"📄 当前配置的模板: {current_template}")
        print(f"📄 模板文件名: {os.path.basename(current_template)}")
        
        return current_template
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 检查所有模板文件中的'自动填充'问题")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 检查当前配置
    current_template = check_current_settings()
    
    # 2. 检查所有模板文件
    template_dir = "../word_zc"
    template_files = [
        "template_15players_fixed.docx",
        "template_15players_fixed.docx", 
        "template_15players_clean.docx",
        "template_15players_backup.docx"
    ]
    
    results = {}
    
    for template_file in template_files:
        template_path = os.path.join(template_dir, template_file)
        is_clean = check_template_file(template_path)
        results[template_file] = is_clean
    
    # 3. 总结结果
    print(f"\n" + "=" * 60)
    print("📋 检查结果总结")
    print("=" * 60)
    
    print(f"📊 模板文件检查结果:")
    for template_file, is_clean in results.items():
        status = "✅ 正常" if is_clean else "❌ 包含'自动填充'"
        current_mark = " (当前使用)" if current_template and template_file in current_template else ""
        print(f"   {template_file}: {status}{current_mark}")
    
    # 4. 分析问题
    problematic_templates = [name for name, is_clean in results.items() if not is_clean]
    
    if problematic_templates:
        print(f"\n🎯 发现问题:")
        print(f"   包含'自动填充'的模板: {problematic_templates}")
        
        if current_template:
            current_template_name = os.path.basename(current_template)
            if current_template_name in problematic_templates:
                print(f"   ❌ 当前使用的模板有问题: {current_template_name}")
                print(f"\n💡 解决方案:")
                print(f"   1. 修改 config/settings.py")
                print(f"   2. 将 TEMPLATE_PATH 改为使用 template_15players_fixed.docx")
                print(f"   3. 或者手动编辑 {current_template_name} 文件")
            else:
                print(f"   ✅ 当前使用的模板正常: {current_template_name}")
                print(f"   问题可能在其他地方")
    else:
        print(f"\n✅ 所有模板文件都正常")
        print(f"   '自动填充'问题可能在数据处理环节")
    
    # 5. 提供具体建议
    print(f"\n💡 建议:")
    if "template_15players_fixed.docx" in results and results["template_15players_fixed.docx"]:
        print(f"   1. 确保使用 template_15players_fixed.docx 模板")
        print(f"   2. 检查 config/settings.py 中的 TEMPLATE_PATH 配置")
    
    if current_template and "template_15players_fixed.docx" in current_template:
        print(f"   3. ⚠️ 当前可能在使用原始模板，建议切换到修复版本")

if __name__ == "__main__":
    main()
