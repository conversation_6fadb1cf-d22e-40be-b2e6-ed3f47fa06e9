#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试干净模板的效果
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import shutil

def test_clean_template_with_correct_path():
    """使用正确路径测试干净模板"""
    print("🧪 测试干净模板（修复路径问题）")
    print("=" * 60)
    
    clean_template_path = "../word_zc/template_15players_clean.docx"
    
    if not os.path.exists(clean_template_path):
        print("❌ 干净模板不存在")
        return False
    
    try:
        # 创建测试数据
        test_data = {
            "teamInfo": {
                "title": "干净模板最终测试报名表",
                "organizationName": "干净模板最终测试队",
                "teamLeader": "张三",
                "coach": "李四",
                "teamDoctor": "王五",
                "contactPerson": "赵六",
                "contactPhone": "13800138000"
            },
            "players": [
                {
                    "number": "10",
                    "name": "测试球员",
                    "photoPath": "java_word_photos/player1.png"
                }
            ],
            "config": {
                "templatePath": "template_15players_clean.docx",  # 相对于Java工作目录
                "outputDir": "output",
                "photosDir": "java_word_photos"
            }
        }
        
        # Java工作目录
        java_dir = "../word_zc/ai-football-generator"
        
        # 写入测试文件到Java目录
        test_file = os.path.join(java_dir, "test_clean_final.json")
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 复制干净模板到Java目录
        java_template_path = os.path.join(java_dir, "template_15players_clean.docx")
        shutil.copy2(clean_template_path, java_template_path)
        
        print(f"✅ 创建测试文件: {test_file}")
        print(f"✅ 复制模板到: {java_template_path}")
        print(f"📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        print(f"\n🚀 运行Java程序...")
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", "test_clean_final.json"],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore',
            cwd=java_dir
        )
        
        print(f"📊 Java返回码: {result.returncode}")
        
        if result.stdout:
            print(f"📝 Java标准输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"   {line}")
        
        if result.stderr:
            print(f"📝 Java错误输出:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    
                    # 特别关注团队信息解析
                    if 'INFO:Team info parsed:' in line:
                        print(f"   🔍 关键: {line}")
                        if '联系人=赵六' in line and '联系电话=13800138000' in line:
                            print("   ✅ 联系人信息解析正确！")
                        else:
                            print("   ⚠️ 联系人信息解析可能有问题")
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 检查生成的文件
            output_dir = os.path.join(java_dir, "output")
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 检查生成文件的内容
                    return check_final_word_content(latest_file)
        else:
            print(f"❌ Java程序运行失败")
        
        return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理测试文件
        cleanup_files = [
            os.path.join(java_dir, "test_clean_final.json"),
            os.path.join(java_dir, "template_15players_clean.docx")
        ]
        for file in cleanup_files:
            if os.path.exists(file):
                os.remove(file)

def check_final_word_content(file_path):
    """检查最终Word文档内容"""
    print(f"\n🔍 检查最终Word文档内容")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "13800138000" in full_text
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    
                    print(f"📄 最终内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'13800138000': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    print(f"   占位符{{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   占位符{{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    
                    # 显示联系人相关的完整上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+10)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            break
                    
                    # 判断最终结果
                    if has_contact_person and has_contact_phone and not has_placeholder_person and not has_placeholder_phone:
                        print(f"\n🎉 完美成功！")
                        print(f"✅ 联系人信息正确显示")
                        print(f"✅ 占位符完全替换")
                        return "perfect"
                    elif has_contact_person and has_contact_phone:
                        print(f"\n✅ 基本成功！")
                        print(f"✅ 联系人信息正确显示")
                        print(f"⚠️ 占位符可能未完全替换，但不影响显示")
                        return "success"
                    else:
                        print(f"\n❌ 仍有问题")
                        print(f"❌ 联系人信息未正确显示")
                        return "failed"
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return "error"
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return "error"

def test_python_integration_with_clean_template():
    """测试Python集成使用干净模板"""
    print(f"\n🧪 测试Python集成使用干净模板")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 准备测试数据
        team_data = {
            'name': 'Python干净模板测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print(f"📄 测试数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 临时替换模板文件
        original_template = "../word_zc/template_15players_fixed.docx"
        clean_template = "../word_zc/template_15players_clean.docx"
        backup_template = "../word_zc/template_15players_backup.docx"
        
        # 备份原模板
        if os.path.exists(original_template):
            shutil.copy2(original_template, backup_template)
        
        # 使用干净模板
        shutil.copy2(clean_template, original_template)
        
        try:
            # 获取配置路径
            paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
            
            # 创建Word生成服务
            word_service = WordGeneratorService(
                jar_path=paths['jar_path'],
                template_path=paths['template_path'],
                output_dir=paths['output_dir']
            )
            
            # 生成Word文档
            print(f"\n🚀 运行Python集成...")
            result = word_service.generate_report(team_data, players_data)
            
            if result['success']:
                print(f"✅ Python集成成功: {result['file_path']}")
                
                # 检查生成文件的内容
                content_result = check_final_word_content(result['file_path'])
                
                return content_result
            else:
                print(f"❌ Python集成失败: {result['message']}")
                return "failed"
                
        finally:
            # 恢复原模板
            if os.path.exists(backup_template):
                shutil.copy2(backup_template, original_template)
                os.remove(backup_template)
            
    except Exception as e:
        print(f"❌ Python集成测试失败: {e}")
        return "error"

def main():
    """主函数"""
    print("🎯 干净模板最终测试")
    print("=" * 70)
    
    # 测试1: Java直接调用
    java_result = test_clean_template_with_correct_path()
    
    # 测试2: Python集成
    python_result = test_python_integration_with_clean_template()
    
    # 最终结论
    print(f"\n📊 最终测试结果")
    print("=" * 70)
    
    print(f"🔍 Java直接调用: {'✅ 成功' if java_result else '❌ 失败'}")
    print(f"🔍 Python集成: {python_result}")
    
    if java_result and python_result in ["perfect", "success"]:
        print(f"\n🎉 联系人信息问题完全解决！")
        print(f"✅ 干净模板工作正常")
        print(f"✅ 联系人信息能够正确显示在Word文档中")
        print(f"✅ 两种调用方式都正常工作")
        
        print(f"\n🔄 建议替换原模板:")
        print(f"   cp ../word_zc/template_15players_clean.docx ../word_zc/template_15players_fixed.docx")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   1. 在AI聊天中输入: '我是张三，电话13800138000'")
        print(f"   2. AI会自动提取并保存联系人信息")
        print(f"   3. 生成Word报名表时，联系人信息会自动填入")
        print(f"   4. 最终的Word文档包含完整的联系人信息")
        
        print(f"\n🎉 联系人信息自动化流程已完整实现！")
        
    elif python_result in ["perfect", "success"]:
        print(f"\n✅ Python集成修复成功！")
        print(f"✅ Streamlit应用中的联系人信息流程已完整")
        print(f"⚠️ Java直接调用可能还有问题，但不影响主要功能")
        
    else:
        print(f"\n⚠️ 仍需进一步调试")
        print(f"💡 建议检查模板引擎配置和数据传递")

if __name__ == "__main__":
    main()
