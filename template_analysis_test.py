#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级模板文件测试脚本
用于确定主项目默认使用的Word模板文件
"""

import os
import zipfile
import re
import json
from datetime import datetime
from pathlib import Path

class TemplateAnalyzer:
    """模板分析器"""
    
    def __init__(self):
        self.template_files = [
            "word_zc/template_15players_backup_before_color_fix.docx",
            "word_zc/template_15players_backup.docx", 
            "word_zc/template_15players_clean.docx",
            "word_zc/template_15players_fixed.docx",
            "word_zc/template_15players.docx"
        ]
        self.results = {}
    
    def analyze_file_properties(self):
        """分析文件属性"""
        print("=" * 80)
        print("📊 阶段1：文件属性分析")
        print("=" * 80)
        
        for template_file in self.template_files:
            if os.path.exists(template_file):
                stat = os.stat(template_file)
                self.results[template_file] = {
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'created': datetime.fromtimestamp(stat.st_ctime),
                    'exists': True
                }
                
                print(f"📄 {os.path.basename(template_file)}")
                print(f"   大小: {stat.st_size:,} 字节")
                print(f"   修改时间: {datetime.fromtimestamp(stat.st_mtime)}")
                print(f"   创建时间: {datetime.fromtimestamp(stat.st_ctime)}")
                print()
            else:
                self.results[template_file] = {'exists': False}
                print(f"❌ {template_file} - 文件不存在")
    
    def extract_placeholders(self, template_path):
        """提取模板占位符"""
        try:
            with zipfile.ZipFile(template_path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
                
                # 查找占位符
                placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                unique_placeholders = list(set(placeholders))
                
                # 检查损坏的占位符
                broken_patterns = [
                    r'\{[^}]*<[^>]*>[^}]*\}',  # 包含XML标签的占位符
                    r'\{[^}]*w:[^}]*\}',       # 包含Word命名空间的占位符
                ]
                
                broken_placeholders = []
                for pattern in broken_patterns:
                    broken_placeholders.extend(re.findall(pattern, content))
                
                return {
                    'placeholders': unique_placeholders,
                    'broken_placeholders': broken_placeholders,
                    'total_count': len(unique_placeholders),
                    'broken_count': len(broken_placeholders)
                }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_placeholders(self):
        """分析所有模板的占位符"""
        print("=" * 80)
        print("📊 阶段2：占位符分析")
        print("=" * 80)
        
        for template_file in self.template_files:
            if os.path.exists(template_file):
                print(f"📄 分析 {os.path.basename(template_file)}")
                
                placeholder_info = self.extract_placeholders(template_file)
                self.results[template_file]['placeholders'] = placeholder_info
                
                if 'error' in placeholder_info:
                    print(f"   ❌ 错误: {placeholder_info['error']}")
                else:
                    print(f"   ✅ 找到 {placeholder_info['total_count']} 个占位符")
                    if placeholder_info['broken_count'] > 0:
                        print(f"   ⚠️  发现 {placeholder_info['broken_count']} 个损坏的占位符")
                    
                    # 显示前5个占位符作为示例
                    if placeholder_info['placeholders']:
                        print("   示例占位符:")
                        for i, placeholder in enumerate(placeholder_info['placeholders'][:5]):
                            print(f"     {i+1}. {placeholder}")
                        if len(placeholder_info['placeholders']) > 5:
                            print(f"     ... 还有 {len(placeholder_info['placeholders']) - 5} 个")
                print()
    
    def check_code_references(self):
        """检查代码引用情况"""
        print("=" * 80)
        print("📊 阶段3：代码引用分析")
        print("=" * 80)
        
        # 检查主要配置文件
        config_files = [
            "streamlit_team_management_modular/config/settings.py",
            "streamlit_team_management_modular/config/env_settings.py",
            "word_zc/ai-football-generator/test_15players_template.json"
        ]
        
        references = {}
        
        for config_file in config_files:
            if os.path.exists(config_file):
                try:
                    with open(config_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 查找模板引用
                    for template_file in self.template_files:
                        template_name = os.path.basename(template_file)
                        if template_name in content:
                            if config_file not in references:
                                references[config_file] = []
                            references[config_file].append(template_name)
                            
                except Exception as e:
                    print(f"❌ 读取 {config_file} 失败: {e}")
        
        # 显示引用结果
        for config_file, templates in references.items():
            print(f"📄 {config_file}")
            for template in templates:
                print(f"   → 引用: {template}")
            print()
        
        self.results['code_references'] = references
    
    def generate_report(self):
        """生成测试报告"""
        print("=" * 80)
        print("📊 测试报告总结")
        print("=" * 80)
        
        # 按修改时间排序
        valid_files = [(f, info) for f, info in self.results.items() 
                      if isinstance(info, dict) and info.get('exists', False)]
        
        if valid_files:
            sorted_files = sorted(valid_files, 
                                key=lambda x: x[1]['modified'], 
                                reverse=True)
            
            print("📅 按修改时间排序（最新到最旧）:")
            for i, (file_path, info) in enumerate(sorted_files, 1):
                filename = os.path.basename(file_path)
                print(f"   {i}. {filename}")
                print(f"      修改时间: {info['modified']}")
                print(f"      文件大小: {info['size']:,} 字节")
                
                if 'placeholders' in info and 'total_count' in info['placeholders']:
                    print(f"      占位符数量: {info['placeholders']['total_count']}")
                    if info['placeholders'].get('broken_count', 0) > 0:
                        print(f"      ⚠️  损坏占位符: {info['placeholders']['broken_count']}")
                print()
        
        # 代码引用分析
        if 'code_references' in self.results:
            print("🔗 代码引用统计:")
            template_ref_count = {}
            for config_file, templates in self.results['code_references'].items():
                for template in templates:
                    template_ref_count[template] = template_ref_count.get(template, 0) + 1
            
            for template, count in sorted(template_ref_count.items(), 
                                        key=lambda x: x[1], reverse=True):
                print(f"   {template}: 被引用 {count} 次")
            print()
        
        # 推荐结论
        print("🎯 推荐结论:")
        print("   基于以上分析，主项目默认使用的模板很可能是:")
        
        # 找到最新修改且被代码引用的文件
        if valid_files and 'code_references' in self.results:
            latest_file = sorted_files[0][0]
            latest_filename = os.path.basename(latest_file)
            
            # 检查是否被代码引用
            is_referenced = any(latest_filename in templates 
                              for templates in self.results['code_references'].values())
            
            if is_referenced:
                print(f"   ✅ {latest_filename}")
                print("   理由: 最新修改时间 + 被代码引用")
            else:
                print(f"   ⚠️  {latest_filename} (最新修改，但未被代码引用)")
                
                # 查找被引用最多的文件
                if template_ref_count:
                    most_referenced = max(template_ref_count.items(), key=lambda x: x[1])
                    print(f"   🔄 或者 {most_referenced[0]} (被引用最多: {most_referenced[1]} 次)")

def main():
    """主函数"""
    print("🚀 企业级模板文件测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    analyzer = TemplateAnalyzer()
    
    # 执行分析
    analyzer.analyze_file_properties()
    analyzer.analyze_placeholders()
    analyzer.check_code_references()
    analyzer.generate_report()
    
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
