#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度分析联系人信息不显示的根本原因
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET

def analyze_json_data_flow():
    """分析JSON数据流转"""
    print("🔍 分析1: JSON数据流转")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 准备测试数据
        team_data = {
            'name': '数据流转分析队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查JSON数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print("📄 Python准备的JSON数据:")
        print(f"   teamInfo: {json.dumps(json_data['teamInfo'], ensure_ascii=False, indent=2)}")
        
        # 检查关键字段
        team_info = json_data['teamInfo']
        print(f"\n🔍 关键字段检查:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        
        # 保存JSON到文件用于Java测试
        test_json_file = "debug_contact_data.json"
        with open(test_json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 保存调试JSON文件: {test_json_file}")
        
        return json_data
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def test_java_data_processing():
    """测试Java数据处理"""
    print("\n🔍 分析2: Java数据处理")
    print("=" * 60)
    
    # 使用上一步生成的JSON文件
    test_json_file = "debug_contact_data.json"
    
    if not os.path.exists(test_json_file):
        print("❌ 调试JSON文件不存在")
        return None
    
    try:
        print(f"📄 使用JSON文件: {test_json_file}")
        
        # 运行Java程序并捕获详细输出
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_json_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        print(f"📊 Java返回码: {result.returncode}")
        
        if result.stdout:
            print("📝 Java标准输出:")
            print(result.stdout)
        
        if result.stderr:
            print("⚠️ Java错误输出:")
            print(result.stderr)
        
        # 检查生成的文件
        output_dir = "../word_zc/ai-football-generator/output"
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            if files:
                latest_file = max([os.path.join(output_dir, f) for f in files], 
                                key=os.path.getmtime)
                print(f"✅ 生成文件: {latest_file}")
                return latest_file
        
        return None
        
    except Exception as e:
        print(f"❌ Java测试失败: {e}")
        return None

def analyze_word_template():
    """分析Word模板结构"""
    print("\n🔍 分析3: Word模板结构")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return None
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            # 读取document.xml文件
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 模板中的占位符:")
                
                # 查找联系人相关的占位符
                contact_placeholders = []
                if '{{contactPerson}}' in content:
                    contact_placeholders.append('{{contactPerson}}')
                    print("   ✅ 找到 {{contactPerson}} 占位符")
                else:
                    print("   ❌ 未找到 {{contactPerson}} 占位符")
                
                if '{{contactPhone}}' in content:
                    contact_placeholders.append('{{contactPhone}}')
                    print("   ✅ 找到 {{contactPhone}} 占位符")
                else:
                    print("   ❌ 未找到 {{contactPhone}} 占位符")
                
                # 查找其他可能的联系人相关占位符
                other_patterns = ['联系人', '联系电话', 'contact', 'phone']
                print("\n📄 其他可能的联系人相关内容:")
                for pattern in other_patterns:
                    if pattern in content:
                        print(f"   ✅ 找到相关内容: {pattern}")
                
                # 显示部分模板内容用于调试
                print(f"\n📄 模板内容片段 (前500字符):")
                print(content[:500] + "...")
                
                return contact_placeholders
                
    except Exception as e:
        print(f"❌ 模板分析失败: {e}")
        return None

def analyze_java_source_code():
    """分析Java源代码中的数据映射"""
    print("\n🔍 分析4: Java源代码数据映射")
    print("=" * 60)
    
    java_files = [
        "../word_zc/ai-football-generator/src/main/java/WordGeneratorCore.java",
        "../word_zc/ai-football-generator/src/main/java/PythonIntegrationAdapter.java"
    ]
    
    mapping_info = {}
    
    for java_file in java_files:
        if os.path.exists(java_file):
            try:
                with open(java_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n📄 分析文件: {java_file}")
                
                # 查找联系人相关的映射
                contact_mappings = []
                
                if 'contactPerson' in content:
                    contact_mappings.append('contactPerson')
                    print("   ✅ 找到 contactPerson 处理")
                    
                    # 查找具体的映射代码
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'contactPerson' in line and ('put' in line or '=' in line):
                            print(f"   📝 第{i+1}行: {line.strip()}")
                
                if 'contactPhone' in content:
                    contact_mappings.append('contactPhone')
                    print("   ✅ 找到 contactPhone 处理")
                    
                    # 查找具体的映射代码
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'contactPhone' in line and ('put' in line or '=' in line):
                            print(f"   📝 第{i+1}行: {line.strip()}")
                
                if not contact_mappings:
                    print("   ❌ 未找到联系人字段处理")
                
                mapping_info[java_file] = contact_mappings
                
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
    
    return mapping_info

def test_direct_java_with_debug():
    """使用调试模式测试Java程序"""
    print("\n🔍 分析5: Java调试模式测试")
    print("=" * 60)
    
    # 创建包含联系人信息的简单测试数据
    debug_data = {
        "teamInfo": {
            "title": "调试测试报名表",
            "organizationName": "调试测试队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "赵六",
            "contactPhone": "13800138000"
        },
        "players": [
            {
                "number": "10",
                "name": "调试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入调试文件
        debug_file = "debug_simple_test.json"
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(debug_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建调试文件: {debug_file}")
        print("📄 调试数据包含:")
        print(f"   contactPerson: {debug_data['teamInfo']['contactPerson']}")
        print(f"   contactPhone: {debug_data['teamInfo']['contactPhone']}")
        
        # 运行Java程序
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", debug_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        print(f"\n📊 Java执行结果:")
        print(f"   返回码: {result.returncode}")
        
        if result.stdout:
            print("   标准输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"     {line}")
        
        if result.stderr:
            print("   错误输出:")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"     {line}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 调试测试失败: {e}")
        return False
    finally:
        # 清理调试文件
        if os.path.exists("debug_simple_test.json"):
            os.remove("debug_simple_test.json")

def main():
    """主分析函数"""
    print("🎯 联系人信息不显示问题 - 深度分析")
    print("=" * 70)
    
    analysis_results = {}
    
    # 分析1: JSON数据流转
    analysis_results['json_data'] = analyze_json_data_flow()
    
    # 分析2: Java数据处理
    analysis_results['java_processing'] = test_java_data_processing()
    
    # 分析3: Word模板结构
    analysis_results['template_structure'] = analyze_word_template()
    
    # 分析4: Java源代码映射
    analysis_results['java_mapping'] = analyze_java_source_code()
    
    # 分析5: Java调试测试
    analysis_results['java_debug'] = test_direct_java_with_debug()
    
    # 综合分析
    print("\n📊 问题根因分析")
    print("=" * 70)
    
    print("🔍 数据流转检查:")
    if analysis_results['json_data']:
        json_data = analysis_results['json_data']
        team_info = json_data.get('teamInfo', {})
        if 'contactPerson' in team_info and 'contactPhone' in team_info:
            print("✅ Python层正确准备了联系人数据")
        else:
            print("❌ Python层缺少联系人数据")
    
    print("\n🔍 模板结构检查:")
    if analysis_results['template_structure']:
        placeholders = analysis_results['template_structure']
        if placeholders:
            print(f"✅ 模板包含占位符: {placeholders}")
        else:
            print("❌ 模板缺少联系人占位符")
    
    print("\n🔍 Java映射检查:")
    if analysis_results['java_mapping']:
        mapping_info = analysis_results['java_mapping']
        has_mapping = any(mappings for mappings in mapping_info.values())
        if has_mapping:
            print("✅ Java代码包含联系人字段映射")
        else:
            print("❌ Java代码缺少联系人字段映射")
    
    print("\n🔍 Java执行检查:")
    if analysis_results['java_debug']:
        print("✅ Java程序执行成功")
    else:
        print("❌ Java程序执行失败")
    
    print("\n🎯 问题定位:")
    print("基于以上分析，问题可能出现在以下环节:")
    print("1. Java代码虽然接收到了联系人数据，但可能没有正确映射到模板")
    print("2. 模板占位符可能存在，但Java的占位符替换逻辑有问题")
    print("3. 数据传递过程中可能存在编码或格式问题")
    
    # 清理调试文件
    debug_files = ["debug_contact_data.json", "debug_simple_test.json"]
    for file in debug_files:
        if os.path.exists(file):
            os.remove(file)

if __name__ == "__main__":
    main()
