#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Word文档内容和模板占位符的测试脚本
"""

import os
import sys
import json
import zipfile
import xml.etree.ElementTree as ET
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from word_generator_service import WordGeneratorService
from config.settings import app_settings

def check_template_placeholders():
    """检查模板文件中的占位符"""
    print("=" * 60)
    print("🔍 检查模板文件中的占位符")
    print("=" * 60)
    
    template_path = "C:\\Users\\<USER>\\Desktop\\test\\comfyui\\00000\\2222\\444-002\\word_zc\\template_15players_fixed.docx"
    
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    try:
        with zipfile.ZipFile(template_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 模板文件分析:")
        
        # 查找所有占位符
        placeholder_pattern = r'\{\{[^}]+\}\}'
        placeholders = re.findall(placeholder_pattern, content)
        
        print(f"   找到占位符数量: {len(placeholders)}")
        
        # 检查颜色相关占位符
        color_placeholders = [
            "{{jerseyColor}}", "{{shortsColor}}", 
            "{{socksColor}}", "{{goalkeeperKitColor}}"
        ]
        
        print(f"\n🎨 颜色占位符检查:")
        for placeholder in color_placeholders:
            if placeholder in content:
                print(f"   ✅ 找到: {placeholder}")
            else:
                print(f"   ❌ 缺失: {placeholder}")
                # 检查是否被分割
                field_name = placeholder.replace("{{", "").replace("}}", "")
                if field_name in content:
                    print(f"      ⚠️ 字段名存在但可能被XML分割: {field_name}")
        
        # 检查联系人占位符
        contact_placeholders = ["{{contactPerson}}", "{{contactPhone}}"]
        
        print(f"\n📞 联系人占位符检查:")
        for placeholder in contact_placeholders:
            if placeholder in content:
                print(f"   ✅ 找到: {placeholder}")
            else:
                print(f"   ❌ 缺失: {placeholder}")
        
        # 检查人员占位符
        personnel_placeholders = ["{{teamLeader}}", "{{coach}}", "{{teamDoctor}}"]
        
        print(f"\n👥 人员占位符检查:")
        for placeholder in personnel_placeholders:
            if placeholder in content:
                print(f"   ✅ 找到: {placeholder}")
            else:
                print(f"   ❌ 缺失: {placeholder}")
        
        print(f"\n📋 所有找到的占位符:")
        for placeholder in sorted(set(placeholders)):
            print(f"   {placeholder}")
        
        return placeholders
        
    except Exception as e:
        print(f"❌ 检查模板失败: {e}")
        return []

def analyze_word_document_xml(docx_path):
    """分析Word文档的XML内容"""
    print(f"\n🔍 分析Word文档XML内容: {os.path.basename(docx_path)}")
    
    if not os.path.exists(docx_path):
        print(f"❌ 文件不存在: {docx_path}")
        return
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 文档XML分析:")
        
        # 查找未替换的占位符
        placeholder_pattern = r'\{\{[^}]+\}\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"   ❌ 发现未替换的占位符 ({len(remaining_placeholders)}个):")
            for placeholder in sorted(set(remaining_placeholders)):
                print(f"      {placeholder}")
        else:
            print(f"   ✅ 所有占位符都已被替换")
        
        # 查找颜色值
        color_words = ["红色", "蓝色", "绿色", "黄色", "白色", "黑色", "粉色", "橙色", "紫色", "灰色"]
        found_colors = []
        for color in color_words:
            if color in content:
                found_colors.append(color)
        
        if found_colors:
            print(f"   ✅ 发现颜色值: {', '.join(found_colors)}")
        else:
            print(f"   ⚠️ 未发现明显的颜色值")
        
        # 查找联系人信息
        phone_pattern = r'1[3-9]\d{9}'
        phones = re.findall(phone_pattern, content)
        if phones:
            print(f"   ✅ 发现电话号码: {', '.join(phones)}")
        else:
            print(f"   ⚠️ 未发现电话号码")
        
        # 查找团队名称
        if "测试" in content:
            print(f"   ✅ 发现测试相关内容")
        
        return {
            "remaining_placeholders": remaining_placeholders,
            "found_colors": found_colors,
            "found_phones": phones
        }
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return None

def test_with_color_data():
    """使用包含颜色数据的测试进行Word生成"""
    print("\n" + "=" * 60)
    print("🔍 使用颜色数据测试Word生成")
    print("=" * 60)
    
    # 创建包含完整颜色数据的测试
    team_data = {
        "name": "颜色验证测试队",
        "leader": "测试领队",
        "coach": "测试教练",
        "doctor": "测试队医",
        "contact_person": "张三",
        "contact_phone": "13812345678",
        # 明确的颜色数据
        "jersey_color": "红色",
        "shorts_color": "白色",
        "socks_color": "红色",
        "goalkeeper_kit_color": "绿色"
    }
    
    players_data = [
        {"name": "球员1", "jersey_number": "1", "photo": ""},
        {"name": "球员2", "jersey_number": "2", "photo": ""}
    ]
    
    print(f"📋 测试数据:")
    print(f"   团队名称: {team_data['name']}")
    print(f"   球衣颜色: {team_data['jersey_color']}")
    print(f"   球裤颜色: {team_data['shorts_color']}")
    print(f"   球袜颜色: {team_data['socks_color']}")
    print(f"   守门员服装颜色: {team_data['goalkeeper_kit_color']}")
    print(f"   联系人: {team_data['contact_person']}")
    print(f"   联系电话: {team_data['contact_phone']}")
    
    try:
        # 创建WordGeneratorService
        paths = app_settings.word_generator.get_absolute_paths("color_verification", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"✅ 生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的文档
            analysis = analyze_word_document_xml(output_file)
            
            return output_file, analysis
        else:
            print(f"❌ 生成失败: {result['message']}")
            return None, None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None, None

def create_summary_report():
    """创建测试总结报告"""
    print("\n" + "=" * 60)
    print("📋 创建测试总结报告")
    print("=" * 60)
    
    # 检查模板
    template_placeholders = check_template_placeholders()
    
    # 测试Word生成
    output_file, analysis = test_with_color_data()
    
    print(f"\n📊 最终测试报告:")
    print(f"   测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n🎯 模板检查结果:")
    if template_placeholders:
        color_placeholders_in_template = [p for p in template_placeholders if 'Color' in p]
        print(f"   模板中的颜色占位符: {len(color_placeholders_in_template)}个")
        for p in color_placeholders_in_template:
            print(f"      {p}")
    
    print(f"\n🎯 Word生成测试结果:")
    if output_file and analysis:
        print(f"   ✅ Word文档生成成功")
        print(f"   文件: {os.path.basename(output_file)}")
        
        if analysis['remaining_placeholders']:
            print(f"   ❌ 未替换的占位符: {len(analysis['remaining_placeholders'])}个")
            for p in analysis['remaining_placeholders']:
                print(f"      {p}")
        else:
            print(f"   ✅ 所有占位符都已替换")
        
        if analysis['found_colors']:
            print(f"   ✅ 发现的颜色: {', '.join(analysis['found_colors'])}")
        else:
            print(f"   ❌ 未发现颜色信息")
        
        if analysis['found_phones']:
            print(f"   ✅ 发现的电话: {', '.join(analysis['found_phones'])}")
    else:
        print(f"   ❌ Word文档生成失败")
    
    print(f"\n🔍 问题总结:")
    print(f"   1. WordGeneratorService数据映射: ✅ 正常")
    print(f"   2. Java程序执行: ✅ 正常")
    print(f"   3. 模板占位符: 需要检查具体占位符")
    print(f"   4. 颜色字段传递: 需要修复fashion_workflow_service.py")
    
    return {
        "template_placeholders": template_placeholders,
        "output_file": output_file,
        "analysis": analysis
    }

def main():
    """主测试函数"""
    print("🚀 开始Word内容验证测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        report = create_summary_report()
        
        print(f"\n✅ 测试完成!")
        print(f"   详细结果请查看上方输出")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
