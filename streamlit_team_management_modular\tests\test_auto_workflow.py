#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动化工作流
Test Automated Workflow

测试图片路径映射和自动Word生成功能
"""

import os
import sys
import json
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_photo_mapping():
    """测试图片路径映射功能（基于用户文件夹）"""
    print("🧪 测试图片路径映射功能（基于用户文件夹）")
    print("=" * 50)

    # 模拟球员数据
    players_with_photos = [
        {
            "id": "player_001",
            "name": "张三",
            "jersey_number": "10",
            "photo_info": {
                "absolute_path": "/path/to/original/zhang_san.jpg"
            }
        },
        {
            "id": "player_002",
            "name": "李四",
            "jersey_number": "11",
            "photo_info": {
                "absolute_path": "/path/to/original/li_si.jpg"
            }
        }
    ]

    # 创建模拟的换装结果文件
    temp_dir = "temp_files"
    os.makedirs(temp_dir, exist_ok=True)

    # 创建模拟图片文件
    temp_files = [
        "temp_files/zhang_san_step3_white_background.png",
        "temp_files/li_si_step3_white_background.png"
    ]

    for temp_file in temp_files:
        with open(temp_file, 'w') as f:
            f.write("mock image data")

    # 模拟换装结果
    fashion_result = {
        "success": True,
        "successful_count": 2,
        "failed_count": 0,
        "results": [
            {
                "success": True,
                "final_result": "temp_files/zhang_san_step3_white_background.png",
                "photo_name": "zhang_san.jpg"
            },
            {
                "success": True,
                "final_result": "temp_files/li_si_step3_white_background.png",
                "photo_name": "li_si.jpg"
            }
        ]
    }

    # 测试映射功能
    try:
        from services.fashion_workflow_service import FashionWorkflowService

        # 创建服务实例
        workflow_service = FashionWorkflowService("test_user")

        # 测试图片映射（现在需要team_name参数）
        team_name = "测试球队"
        mapping = workflow_service._create_player_photo_mapping(
            players_with_photos, fashion_result, team_name
        )

        print("📋 映射结果:")
        for player_id, photo_path in mapping.items():
            print(f"  {player_id} -> {photo_path}")

        # 验证映射结果（现在应该指向用户文件夹）
        user_data_path = workflow_service.auth_service.get_user_data_path("test_user", "processed_photos")
        expected_paths = [
            os.path.join(user_data_path, team_name, "player_001_张三_fashion_final.png"),
            os.path.join(user_data_path, team_name, "player_002_李四_fashion_final.png")
        ]

        # 检查是否创建了用户文件夹中的图片
        success = True
        for player_id, photo_path in mapping.items():
            if not photo_path.startswith(user_data_path):
                print(f"❌ 图片路径不在用户文件夹中: {photo_path}")
                success = False

        if success and len(mapping) == 2:
            print("✅ 图片路径映射测试通过！")
            return True
        else:
            print("❌ 图片路径映射测试失败！")
            return False

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                os.remove(temp_file)

def test_workflow_integration():
    """测试工作流集成"""
    print("\n🧪 测试工作流集成")
    print("=" * 50)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建服务实例
        workflow_service = FashionWorkflowService("test_user")
        
        # 检查服务是否正确初始化
        if hasattr(workflow_service, '_create_player_photo_mapping'):
            print("✅ _create_player_photo_mapping 方法存在")
        else:
            print("❌ _create_player_photo_mapping 方法不存在")
            return False
            
        if hasattr(workflow_service, '_auto_generate_word_document'):
            print("✅ _auto_generate_word_document 方法存在")
        else:
            print("❌ _auto_generate_word_document 方法不存在")
            return False
        
        print("✅ 工作流集成测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_folder_system():
    """测试用户文件夹系统"""
    print("\n🧪 测试用户文件夹系统")
    print("=" * 50)

    try:
        from services.auth_service import AuthService

        # 创建认证服务
        auth_service = AuthService()
        test_user_id = "test_user_12345"

        # 测试创建用户文件夹
        user_folder = auth_service.create_user_data_folder(test_user_id)
        print(f"✅ 用户文件夹创建成功: {user_folder}")

        # 测试获取各种数据路径
        paths_to_test = ["teams", "photos", "exports", "processed_photos", "word_output"]
        for path_type in paths_to_test:
            if path_type in ["processed_photos", "word_output"]:
                # 这些是我们新增的路径
                path = os.path.join(auth_service.get_user_data_path(test_user_id), path_type)
                os.makedirs(path, exist_ok=True)
            else:
                path = auth_service.get_user_data_path(test_user_id, path_type)

            if os.path.exists(path):
                print(f"✅ {path_type} 路径存在: {path}")
            else:
                print(f"❌ {path_type} 路径不存在: {path}")
                return False

        print("✅ 用户文件夹系统测试通过！")
        return True

    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_structure():
    """测试数据结构兼容性"""
    print("\n🧪 测试数据结构兼容性")
    print("=" * 50)
    
    # 模拟完整的工作流结果
    workflow_result = {
        "team_name": "测试球队",
        "execution_time": datetime.now().isoformat(),
        "readiness_check": {"ready": True},
        "fashion_result": {
            "success": True,
            "successful_count": 2,
            "results": []
        },
        "clothes_image_path": "/path/to/clothes.png",
        "processed_players": 2,
        "player_photo_mapping": {
            "player_001": "temp_files/player1_final.png",
            "player_002": "temp_files/player2_final.png"
        },
        "word_generation_result": {
            "success": True,
            "file_path": "word_output/test_team_report.docx"
        }
    }
    
    # 验证数据结构
    required_fields = [
        "team_name", "execution_time", "fashion_result", 
        "player_photo_mapping", "word_generation_result"
    ]
    
    missing_fields = []
    for field in required_fields:
        if field not in workflow_result:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"❌ 缺少必需字段: {missing_fields}")
        return False
    else:
        print("✅ 数据结构兼容性测试通过！")
        return True

def main():
    """主测试函数"""
    print("🚀 开始自动化工作流测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    test_results.append(("用户文件夹系统", test_user_folder_system()))
    test_results.append(("图片路径映射", test_photo_mapping()))
    test_results.append(("工作流集成", test_workflow_integration()))
    test_results.append(("数据结构兼容性", test_data_structure()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！自动化工作流功能已就绪。")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查实现。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
