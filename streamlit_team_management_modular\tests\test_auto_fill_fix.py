#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动填充逻辑修复
"""

import zipfile
import xml.etree.ElementTree as ET
import os

def test_auto_fill_fix():
    """测试自动填充逻辑修复"""
    print("🔍 测试自动填充逻辑修复")
    print("=" * 60)
    print("期望：'自动填充' -> '赵六'")
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_c61aa17e3868'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_c61aa17e3868')
        
        # 测试修复后的Word生成
        print("📄 测试修复后的Word生成")
        
        word_result = workflow_service._auto_generate_word_document(
            "天依369", {}, None
        )
        
        print(f"   Word生成结果: {word_result}")
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            
            # 检查生成的文件内容
            file_path = word_result.get('file_path')
            if file_path and os.path.exists(file_path):
                return analyze_auto_fill_result(file_path)
            else:
                print("❌ 生成的文件不存在")
                return False
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_auto_fill_result(file_path):
    """分析自动填充结果"""
    print(f"\n🔍 分析自动填充结果")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 修复后Word文档内容:")
                    print(f"   {full_text}")
                    
                    # 分析自动填充效果
                    analysis = {}
                    
                    # 1. 检查是否还有"自动填充"
                    auto_fill_count = full_text.count("自动填充")
                    analysis['auto_fill_remaining'] = auto_fill_count
                    
                    print(f"\n📋 自动填充修复检查:")
                    if auto_fill_count == 0:
                        print(f"   ✅ 无'自动填充'字样，修复成功")
                    else:
                        print(f"   ❌ 仍有{auto_fill_count}个'自动填充'字样")
                    
                    # 2. 检查联系人是否正确填充到人员信息
                    contact_person = "赵六"
                    zhao_liu_count = full_text.count(contact_person)
                    analysis['contact_person_count'] = zhao_liu_count
                    
                    print(f"\n📋 联系人自动填充检查:")
                    print(f"   联系人'{contact_person}'出现次数: {zhao_liu_count}")
                    
                    # 期望：联系人信息1次 + 领队1次 + 教练1次 + 队医1次 = 4次
                    if zhao_liu_count >= 4:
                        print(f"   ✅ 联系人信息正确自动填充到多个人员字段")
                        analysis['auto_fill_success'] = True
                    elif zhao_liu_count >= 2:
                        print(f"   ⚠️ 联系人信息部分自动填充")
                        analysis['auto_fill_success'] = False
                    else:
                        print(f"   ❌ 联系人信息自动填充失败")
                        analysis['auto_fill_success'] = False
                    
                    # 3. 检查具体的人员信息区域
                    print(f"\n📋 人员信息区域检查:")
                    words = full_text.split()
                    
                    # 查找领队区域
                    for i, word in enumerate(words):
                        if "领队" in word:
                            start = max(0, i-2)
                            end = min(len(words), i+5)
                            context = ' '.join(words[start:end])
                            print(f"   领队区域: {context}")
                            analysis['leader_context'] = context
                            break
                    
                    # 查找教练区域
                    for i, word in enumerate(words):
                        if "教练" in word:
                            start = max(0, i-2)
                            end = min(len(words), i+5)
                            context = ' '.join(words[start:end])
                            print(f"   教练区域: {context}")
                            analysis['coach_context'] = context
                            break
                    
                    # 查找队医区域
                    for i, word in enumerate(words):
                        if "队医" in word:
                            start = max(0, i-2)
                            end = min(len(words), i+5)
                            context = ' '.join(words[start:end])
                            print(f"   队医区域: {context}")
                            analysis['doctor_context'] = context
                            break
                    
                    return analysis
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def verify_auto_fill_logic():
    """验证自动填充逻辑"""
    print(f"\n🔍 验证自动填充逻辑")
    print("=" * 60)
    
    try:
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_c61aa17e3868')
        
        # 模拟AI数据
        test_ai_data = {
            "team_info": {
                "ai_extracted_info": {
                    "basic_info": {
                        "contact_person": "赵六",
                        "contact_phone": "18454432036",
                        "leader_name": "自动填充",
                        "team_doctor": "自动填充"
                    },
                    "additional_info": {
                        "coach_name": "自动填充"
                    }
                }
            }
        }
        
        print(f"📄 模拟AI数据:")
        basic_info = test_ai_data["team_info"]["ai_extracted_info"]["basic_info"]
        additional_info = test_ai_data["team_info"]["ai_extracted_info"]["additional_info"]
        
        print(f"   联系人: '{basic_info['contact_person']}'")
        print(f"   领队: '{basic_info['leader_name']}'")
        print(f"   教练: '{additional_info['coach_name']}'")
        print(f"   队医: '{basic_info['team_doctor']}'")
        
        # 测试自动填充逻辑
        def auto_fill_with_contact(value, contact_person):
            """自动填充逻辑：如果值是'自动填充'，则使用联系人信息"""
            if value == "自动填充":
                return contact_person
            elif value and value not in ["待定", "未知", "暂无", ""]:
                return value
            return None
        
        contact_person = basic_info["contact_person"]
        
        leader_result = auto_fill_with_contact(basic_info["leader_name"], contact_person)
        coach_result = auto_fill_with_contact(additional_info["coach_name"], contact_person)
        doctor_result = auto_fill_with_contact(basic_info["team_doctor"], contact_person)
        
        print(f"\n📄 自动填充逻辑测试结果:")
        print(f"   领队: '{basic_info['leader_name']}' -> '{leader_result}' {'✅' if leader_result == contact_person else '❌'}")
        print(f"   教练: '{additional_info['coach_name']}' -> '{coach_result}' {'✅' if coach_result == contact_person else '❌'}")
        print(f"   队医: '{basic_info['team_doctor']}' -> '{doctor_result}' {'✅' if doctor_result == contact_person else '❌'}")
        
        return {
            'leader': leader_result,
            'coach': coach_result,
            'doctor': doctor_result,
            'all_correct': all(x == contact_person for x in [leader_result, coach_result, doctor_result])
        }
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 测试自动填充逻辑修复")
    print("=" * 70)
    print("修复目标：'自动填充' -> 联系人姓名 '赵六'")
    print("=" * 70)
    
    # 1. 验证自动填充逻辑
    logic_result = verify_auto_fill_logic()
    
    # 2. 测试实际Word生成
    word_result = test_auto_fill_fix()
    
    # 综合分析
    print(f"\n📊 自动填充修复测试结果")
    print("=" * 70)
    
    if logic_result and logic_result['all_correct']:
        print("✅ 自动填充逻辑修复成功")
        print("   '自动填充' 正确转换为联系人姓名")
    else:
        print("❌ 自动填充逻辑仍有问题")
    
    if word_result:
        if word_result.get('auto_fill_success'):
            print("✅ Word文档中自动填充正常工作")
            print("   联系人信息正确填充到人员字段")
        else:
            print("⚠️ Word文档中自动填充部分工作")
        
        if word_result.get('auto_fill_remaining', 0) == 0:
            print("✅ 无'自动填充'占位符残留")
        else:
            print(f"❌ 仍有{word_result.get('auto_fill_remaining', 0)}个'自动填充'占位符")
    else:
        print("❌ Word生成测试失败")
    
    # 最终结论
    print(f"\n🎯 最终结论:")
    
    if (logic_result and logic_result['all_correct'] and 
        word_result and word_result.get('auto_fill_success') and 
        word_result.get('auto_fill_remaining', 0) == 0):
        
        print("🎉 自动填充逻辑修复完全成功！")
        print("✅ '自动填充' -> '赵六' 转换正常")
        print("✅ 联系人信息正确填充到领队、教练、队医")
        print("✅ Word文档中无'自动填充'占位符")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   1. 看到领队显示为'赵六'而不是'自动填充'")
        print(f"   2. 看到教练显示为'赵六'而不是'自动填充'")
        print(f"   3. 看到队医显示为'赵六'而不是'自动填充'")
        print(f"   4. 获得完整的Word报名表")
        
    else:
        print("⚠️ 自动填充逻辑修复部分成功")
        print("💡 可能需要进一步调试")

if __name__ == "__main__":
    main()
