#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前的自动填充逻辑
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.ai_service import AIService
from services.enhanced_ai_assistant import EnhancedAIAssistant

def test_personnel_auto_fill():
    """测试人员自动填充逻辑"""
    print("=" * 60)
    print("🔍 测试人员自动填充逻辑")
    print("=" * 60)
    
    # 创建AI服务实例
    ai_service = AIService()
    
    # 测试数据：只提供联系人信息
    test_info = {
        "contact_person": "张三",
        "contact_phone": "13812345678",
        "jersey_color": "粉色"
    }
    
    print(f"📄 输入数据:")
    for key, value in test_info.items():
        print(f"   {key}: '{value}'")
    
    # 应用智能填充逻辑
    filled_info = ai_service._apply_smart_fill_logic(test_info)
    
    print(f"\n📄 自动填充后的结果:")
    for key, value in filled_info.items():
        print(f"   {key}: '{value}'")
    
    # 检查人员填充结果
    print(f"\n📋 人员自动填充检查:")
    contact_person = filled_info.get("contact_person", "")
    leader_name = filled_info.get("leader_name", "")
    team_doctor = filled_info.get("team_doctor", "")
    
    print(f"   联系人: '{contact_person}'")
    print(f"   领队: '{leader_name}' {'✅' if leader_name == contact_person else '❌'}")
    print(f"   队医: '{team_doctor}' {'✅' if team_doctor == contact_person else '❌'}")
    
    return filled_info

def test_color_auto_fill():
    """测试颜色自动填充逻辑"""
    print(f"\n" + "=" * 60)
    print("🔍 测试颜色自动填充逻辑")
    print("=" * 60)
    
    # 测试不同颜色的自动填充
    test_colors = ["红色", "蓝色", "白色", "绿色", "粉色", "紫色", "橙色"]
    
    ai_service = AIService()
    
    for jersey_color in test_colors:
        print(f"\n🎨 测试球衣颜色: '{jersey_color}'")
        
        test_info = {
            "contact_person": "测试联系人",
            "contact_phone": "13800000000",
            "jersey_color": jersey_color
        }
        
        # 应用智能填充逻辑
        filled_info = ai_service._apply_smart_fill_logic(test_info)
        
        print(f"   球衣颜色: '{filled_info.get('jersey_color', '')}'")
        print(f"   球裤颜色: '{filled_info.get('shorts_color', '')}'")
        print(f"   球袜颜色: '{filled_info.get('socks_color', '')}'")
        print(f"   守门员服装颜色: '{filled_info.get('goalkeeper_kit_color', '')}'")

def test_enhanced_ai_assistant():
    """测试增强AI助手的自动填充"""
    print(f"\n" + "=" * 60)
    print("🔍 测试增强AI助手的自动填充")
    print("=" * 60)
    
    # 创建增强AI助手实例
    enhanced_ai = EnhancedAIAssistant()
    
    # 模拟用户输入
    user_input = "我是李四，电话13987654321，我们队的球衣是粉色的"
    
    print(f"📄 用户输入: '{user_input}'")
    
    # 提取信息
    extracted_info = enhanced_ai.extract_team_info(user_input)
    
    print(f"\n📄 提取的信息:")
    for section, data in extracted_info.items():
        print(f"   {section}:")
        if isinstance(data, dict):
            for key, value in data.items():
                print(f"      {key}: '{value}'")
        else:
            print(f"      {data}")

def test_workflow_auto_fill():
    """测试工作流中的自动填充逻辑"""
    print(f"\n" + "=" * 60)
    print("🔍 测试工作流中的自动填充逻辑")
    print("=" * 60)
    
    # 模拟AI导出数据
    ai_export_data = {
        "team_info": {
            "name": "自动填充测试队",
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "自动填充测试队",
                    "contact_person": "王五",
                    "contact_phone": "13666777888",
                    "leader_name": "自动填充",
                    "team_doctor": "自动填充"
                },
                "kit_colors": {
                    "jersey_color": "粉色",
                    "shorts_color": "",
                    "socks_color": "",
                    "goalkeeper_kit_color": ""
                },
                "additional_info": {
                    "coach_name": "自动填充"
                }
            }
        }
    }
    
    print(f"📄 AI导出数据:")
    basic_info = ai_export_data["team_info"]["ai_extracted_info"]["basic_info"]
    kit_colors = ai_export_data["team_info"]["ai_extracted_info"]["kit_colors"]
    additional_info = ai_export_data["team_info"]["ai_extracted_info"]["additional_info"]
    
    print(f"   联系人: '{basic_info['contact_person']}'")
    print(f"   领队: '{basic_info['leader_name']}'")
    print(f"   教练: '{additional_info['coach_name']}'")
    print(f"   队医: '{basic_info['team_doctor']}'")
    print(f"   球衣颜色: '{kit_colors['jersey_color']}'")
    
    # 模拟工作流的自动填充逻辑
    def is_valid_value(value):
        """检查值是否有效（不是占位符）"""
        if not value or value in ["待定", "未知", "暂无", ""]:
            return False
        return True

    def auto_fill_with_contact(value, contact_person):
        """自动填充逻辑：如果值是'自动填充'，则使用联系人信息"""
        if value == "自动填充":
            return contact_person
        elif is_valid_value(value):
            return value
        return None
    
    contact_person = basic_info.get("contact_person", "")
    
    # 应用自动填充逻辑
    team_data = {"name": "自动填充测试队"}
    
    # 联系人信息
    if is_valid_value(contact_person):
        team_data["contact_person"] = contact_person
    if is_valid_value(basic_info.get("contact_phone")):
        team_data["contact_phone"] = basic_info.get("contact_phone")
    
    # 人员信息自动填充
    leader_value = auto_fill_with_contact(basic_info.get("leader_name"), contact_person)
    if leader_value:
        team_data["leader"] = leader_value
    
    coach_value = auto_fill_with_contact(additional_info.get("coach_name"), contact_person)
    if coach_value:
        team_data["coach"] = coach_value
    
    doctor_value = auto_fill_with_contact(basic_info.get("team_doctor"), contact_person)
    if doctor_value:
        team_data["doctor"] = doctor_value
    
    # 颜色信息
    if is_valid_value(kit_colors.get("jersey_color")):
        team_data["jersey_color"] = kit_colors.get("jersey_color")
    
    print(f"\n📄 工作流自动填充结果:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 检查自动填充效果
    print(f"\n📋 自动填充效果检查:")
    print(f"   联系人 → 领队: {'✅' if team_data.get('leader') == contact_person else '❌'}")
    print(f"   联系人 → 教练: {'✅' if team_data.get('coach') == contact_person else '❌'}")
    print(f"   联系人 → 队医: {'✅' if team_data.get('doctor') == contact_person else '❌'}")
    
    return team_data

def main():
    """主测试函数"""
    print("🚀 测试当前自动填充逻辑")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 测试人员自动填充
        personnel_result = test_personnel_auto_fill()
        
        # 2. 测试颜色自动填充
        test_color_auto_fill()
        
        # 3. 测试增强AI助手
        test_enhanced_ai_assistant()
        
        # 4. 测试工作流自动填充
        workflow_result = test_workflow_auto_fill()
        
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        print("🎯 发现的问题:")
        
        # 检查人员自动填充
        if personnel_result.get("leader_name") == personnel_result.get("contact_person"):
            print("   ✅ AI服务人员自动填充正常")
        else:
            print("   ❌ AI服务人员自动填充有问题")
        
        # 检查颜色自动填充
        if personnel_result.get("shorts_color"):
            print("   ✅ AI服务颜色自动填充正常")
        else:
            print("   ❌ AI服务颜色自动填充有问题")
        
        # 检查工作流自动填充
        if workflow_result.get("leader") == workflow_result.get("contact_person"):
            print("   ✅ 工作流人员自动填充正常")
        else:
            print("   ❌ 工作流人员自动填充有问题")
        
        print(f"\n💡 改进建议:")
        print(f"   1. 扩展颜色自动填充逻辑，支持更多颜色（如粉色、紫色等）")
        print(f"   2. 确保工作流和AI服务的自动填充逻辑一致")
        print(f"   3. 添加更智能的颜色搭配逻辑")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
