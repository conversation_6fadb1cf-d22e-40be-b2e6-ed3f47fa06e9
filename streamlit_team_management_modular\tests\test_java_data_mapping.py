#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Java数据映射的具体问题
"""

import json
import subprocess
import os

def test_java_data_mapping_debug():
    """测试Java数据映射的调试信息"""
    print("🔍 测试Java数据映射调试")
    print("=" * 60)
    
    # 创建包含联系人信息的测试数据
    test_data = {
        "teamInfo": {
            "title": "Java映射调试报名表",
            "organizationName": "Java映射调试队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "赵六",
            "contactPhone": "13800138000"
        },
        "players": [
            {
                "number": "10",
                "name": "调试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入测试文件
        test_file = "java_mapping_debug.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件: {test_file}")
        print("📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序并捕获详细输出
        print("\n🚀 运行Java程序...")
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stdout:
            print("\n📝 标准输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"   {line}")
        
        if result.stderr:
            print("\n📝 错误输出 (包含调试信息):")
            for line in result.stderr.split('\n'):
                if line.strip():
                    print(f"   {line}")
                    
                    # 特别关注联系人相关的日志
                    if 'contact' in line.lower() or '联系' in line:
                        print(f"   🔍 联系人相关: {line}")
        
        # 检查生成的文件
        output_dir = "../word_zc/ai-football-generator/output"
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
            if files:
                latest_file = max([os.path.join(output_dir, f) for f in files], 
                                key=os.path.getmtime)
                print(f"\n✅ 生成文件: {latest_file}")
                
                # 检查文件大小
                file_size = os.path.getsize(latest_file)
                print(f"📊 文件大小: {file_size:,} 字节")
                
                return latest_file
        
        return None
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None
    finally:
        # 清理测试文件
        if os.path.exists("java_mapping_debug.json"):
            os.remove("java_mapping_debug.json")

def analyze_java_log_output():
    """分析Java日志输出中的问题"""
    print("\n🔍 分析Java日志输出")
    print("=" * 60)
    
    print("📄 从之前的测试中观察到的问题:")
    print("   1. Java日志显示: 'ϵ=, ϵ绰=' (联系人和联系电话为空)")
    print("   2. 这表明Java接收到了字段，但值为空")
    print("   3. 可能的原因:")
    print("      • JSON解析问题")
    print("      • 字段映射问题") 
    print("      • 数据类型转换问题")
    
    print("\n💡 需要检查的环节:")
    print("   1. PythonIntegrationAdapter.java 中的 convertTeamInfo 方法")
    print("   2. TeamInfo 类的 getter/setter 方法")
    print("   3. WordGeneratorCore.java 中的 prepareTemplateData 方法")

def create_minimal_test():
    """创建最小化测试来隔离问题"""
    print("\n🔍 创建最小化测试")
    print("=" * 60)
    
    # 创建最简单的测试数据
    minimal_data = {
        "teamInfo": {
            "contactPerson": "测试联系人",
            "contactPhone": "12345678901"
        },
        "players": [],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入最小测试文件
        test_file = "minimal_contact_test.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(minimal_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建最小测试文件: {test_file}")
        print("📄 只包含联系人信息:")
        print(f"   contactPerson: '{minimal_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{minimal_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        print("\n🚀 运行最小化测试...")
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stderr:
            print("\n📝 关键日志信息:")
            for line in result.stderr.split('\n'):
                if 'INFO:Team info parsed:' in line:
                    print(f"   🔍 团队信息解析: {line}")
                elif 'contact' in line.lower() or '联系' in line:
                    print(f"   🔍 联系人相关: {line}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 最小化测试失败: {e}")
        return False
    finally:
        if os.path.exists("minimal_contact_test.json"):
            os.remove("minimal_contact_test.json")

def main():
    """主测试函数"""
    print("🎯 Java数据映射问题诊断")
    print("=" * 70)
    
    # 测试1: Java数据映射调试
    generated_file = test_java_data_mapping_debug()
    
    # 测试2: 分析日志输出
    analyze_java_log_output()
    
    # 测试3: 最小化测试
    minimal_success = create_minimal_test()
    
    print("\n📊 诊断结果")
    print("=" * 70)
    
    print("🔍 问题定位:")
    print("1. ✅ 模板中有联系人占位符: {{contactPerson}} 和 {{contactPhone}}")
    print("2. ✅ Python正确准备了联系人数据")
    print("3. ✅ Java程序能够运行并生成文件")
    print("4. ❌ Java日志显示联系人字段为空值")
    
    print("\n🎯 问题根因:")
    print("Java代码在处理联系人字段时，虽然接收到了字段名，但值为空")
    print("这可能是因为:")
    print("• JSON解析时字段映射不正确")
    print("• TeamInfo对象的setter/getter有问题")
    print("• 数据传递过程中丢失了值")
    
    print("\n🔧 下一步修复:")
    print("1. 检查PythonIntegrationAdapter中的字段映射")
    print("2. 验证TeamInfo类的方法实现")
    print("3. 添加更详细的调试日志")
    print("4. 确保数据正确传递到模板引擎")

if __name__ == "__main__":
    main()
