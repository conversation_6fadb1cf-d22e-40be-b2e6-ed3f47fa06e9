#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查实际生成的Word文档内容
"""

import zipfile
import xml.etree.ElementTree as ET
import os

def check_actual_word_document():
    """检查实际生成的Word文档"""
    print("🔍 检查实际生成的Word文档内容")
    print("=" * 60)
    
    word_file = "data/user_b7edc3878c81/word_output/足球队_registration_1756542352495.docx"
    
    if not os.path.exists(word_file):
        print(f"❌ Word文件不存在: {word_file}")
        return False
    
    try:
        with zipfile.ZipFile(word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print("📄 Word文档内容分析:")
                    
                    # 检查联系人信息
                    has_contact_person_zhaoliu = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person_zhaoliu else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 检查占位符是否还存在
                    has_placeholder_person = "{{contactPerson}}" in content
                    has_placeholder_phone = "{{contactPhone}}" in content
                    has_placeholder_spaced_person = "{{contact Person}}" in content
                    has_placeholder_spaced_phone = "{{contact Phone}}" in content
                    
                    print(f"\n📄 占位符检查:")
                    print(f"   {{{{contactPerson}}}}: {'⚠️ 仍存在' if has_placeholder_person else '✅ 已替换'}")
                    print(f"   {{{{contactPhone}}}}: {'⚠️ 仍存在' if has_placeholder_phone else '✅ 已替换'}")
                    print(f"   {{{{contact Person}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_person else '✅ 已替换'}")
                    print(f"   {{{{contact Phone}}}}: {'⚠️ 仍存在' if has_placeholder_spaced_phone else '✅ 已替换'}")
                    
                    # 显示联系人相关的上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    contact_found = False
                    
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            contact_found = True
                            break
                    
                    if not contact_found:
                        print("   ❌ 未找到联系人相关内容")
                        
                        # 搜索可能的联系人相关内容
                        contact_keywords = ["联系", "电话", "赵六", "18454432036", "contact"]
                        for keyword in contact_keywords:
                            if keyword in full_text:
                                # 找到关键词的位置
                                words = full_text.split()
                                for i, word in enumerate(words):
                                    if keyword in word:
                                        start = max(0, i-5)
                                        end = min(len(words), i+10)
                                        context = ' '.join(words[start:end])
                                        print(f"   找到'{keyword}': {context}")
                                        break
                                break
                    
                    # 显示文档的主要内容结构
                    print(f"\n📄 文档主要内容:")
                    lines = full_text.split('\n')
                    content_lines = [line.strip() for line in lines if line.strip()]
                    
                    for i, line in enumerate(content_lines[:20]):  # 显示前20行
                        if line:
                            print(f"   第{i+1}行: {line}")
                    
                    if len(content_lines) > 20:
                        print(f"   ... (还有{len(content_lines)-20}行)")
                    
                    return {
                        'has_contact_person': has_contact_person_zhaoliu,
                        'has_contact_phone': has_contact_phone,
                        'has_placeholders': any([has_placeholder_person, has_placeholder_phone, 
                                               has_placeholder_spaced_person, has_placeholder_spaced_phone]),
                        'contact_found': contact_found,
                        'full_text': full_text
                    }
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def check_ai_data_mapping():
    """检查AI数据映射"""
    print(f"\n🔍 检查AI数据映射")
    print("=" * 60)
    
    ai_data_file = "data/user_b7edc3878c81/enhanced_ai_data/803_ai_data.json"
    
    if not os.path.exists(ai_data_file):
        print(f"❌ AI数据文件不存在: {ai_data_file}")
        return False
    
    try:
        import json
        with open(ai_data_file, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        
        print("📄 AI提取的联系人信息:")
        extracted_info = ai_data.get('extracted_info', {})
        basic_info = extracted_info.get('basic_info', {})
        
        contact_person = basic_info.get('contact_person', 'MISSING')
        contact_phone = basic_info.get('contact_phone', 'MISSING')
        leader_name = basic_info.get('leader_name', 'MISSING')
        
        print(f"   contact_person: '{contact_person}'")
        print(f"   contact_phone: '{contact_phone}'")
        print(f"   leader_name: '{leader_name}'")
        
        return {
            'contact_person': contact_person,
            'contact_phone': contact_phone,
            'leader_name': leader_name
        }
        
    except Exception as e:
        print(f"❌ 检查AI数据失败: {e}")
        return False

def test_word_generation_with_actual_data():
    """使用实际数据测试Word生成"""
    print(f"\n🔍 使用实际数据测试Word生成")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 使用实际的AI数据
        team_data = {
            'name': '803',
            'leader': '赵六',
            'coach': '待定',
            'doctor': '待定',
            'contact_person': '赵六',
            'contact_phone': '18454432036'
        }
        
        players_data = [
            {
                'name': '张三',
                'jersey_number': '1',
                'photo': 'data/user_b7edc3878c81/photos/803/5ca89f6456b34d09b462f161866a0ffe.jpg'
            }
        ]
        
        print(f"📄 使用的实际数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 获取配置
        paths = app_settings.word_generator.get_absolute_paths("actual_test", app_settings.paths)
        
        # 创建服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"\n📄 准备的JSON数据:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        
        # 生成Word
        print(f"\n🚀 运行实际数据生成...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ 生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查新生成的文件
            return check_new_generated_file(result['file_path'])
        else:
            print(f"❌ 生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_new_generated_file(file_path):
    """检查新生成的文件"""
    print(f"\n🔍 检查新生成的文件: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"📄 新文件内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人上下文
                    if has_contact_person or has_contact_phone:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word or "赵六" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                    
                    return has_contact_person and has_contact_phone
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 检查实际Word文档和联系人信息")
    print("=" * 70)
    
    # 1. 检查实际生成的Word文档
    word_result = check_actual_word_document()
    
    # 2. 检查AI数据映射
    ai_data = check_ai_data_mapping()
    
    # 3. 使用实际数据重新测试生成
    new_generation_result = test_word_generation_with_actual_data()
    
    # 综合分析
    print(f"\n📊 综合分析结果")
    print("=" * 70)
    
    if word_result:
        if word_result.get('has_contact_person') and word_result.get('has_contact_phone'):
            print("✅ 实际Word文档中包含联系人信息")
        else:
            print("❌ 实际Word文档中缺少联系人信息")
            
            if word_result.get('has_placeholders'):
                print("   ⚠️ 文档中仍有未替换的占位符")
            else:
                print("   ⚠️ 占位符已替换但联系人信息为空")
    
    if ai_data:
        if ai_data.get('contact_person') != 'MISSING' and ai_data.get('contact_phone') != 'MISSING':
            print("✅ AI数据中包含联系人信息")
        else:
            print("❌ AI数据中缺少联系人信息")
    
    if new_generation_result:
        print("✅ 重新生成的Word文档包含联系人信息")
    else:
        print("❌ 重新生成的Word文档仍缺少联系人信息")
    
    # 最终结论
    print(f"\n🎯 问题诊断:")
    
    if word_result and not (word_result.get('has_contact_person') and word_result.get('has_contact_phone')):
        if ai_data and ai_data.get('contact_person') != 'MISSING':
            print("🔍 AI数据正确，但Word生成有问题")
            print("💡 可能原因:")
            print("   1. 模板占位符仍被分割")
            print("   2. 数据映射逻辑有问题")
            print("   3. poi-tl模板引擎配置问题")
        else:
            print("🔍 AI数据提取有问题")
            print("💡 需要检查AI聊天数据提取逻辑")
    elif new_generation_result:
        print("🎉 问题已解决！重新生成的文档包含联系人信息")
    else:
        print("🔍 需要进一步调试模板和数据映射")

if __name__ == "__main__":
    main()
