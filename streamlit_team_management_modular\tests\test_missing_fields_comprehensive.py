#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试Word生成中缺失字段的测试脚本
特别关注球衣颜色等可能缺失的字段
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from word_generator_service import WordGeneratorService
from config.settings import app_settings
from utils.debug_utils import debug

def test_color_field_data_flow():
    """测试颜色字段的数据流转"""
    print("=" * 60)
    print("🔍 测试颜色字段数据流转")
    print("=" * 60)
    
    # 1. 模拟AI数据结构（实际存储格式）
    ai_export_data = {
        "team_info": {
            "name": "颜色测试队",
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "颜色测试队",
                    "contact_person": "测试联系人",
                    "contact_phone": "12345678901",
                    "leader_name": "自动填充",
                    "team_doctor": "自动填充"
                },
                "kit_colors": {
                    "jersey_color": "红色",
                    "shorts_color": "黑色", 
                    "socks_color": "红色",
                    "goalkeeper_kit_color": "绿色"
                },
                "additional_info": {
                    "coach_name": "自动填充"
                }
            }
        }
    }
    
    print("📄 AI数据中的颜色字段:")
    kit_colors = ai_export_data["team_info"]["ai_extracted_info"]["kit_colors"]
    for field, value in kit_colors.items():
        print(f"   {field}: '{value}'")
    
    # 2. 模拟当前的错误读取逻辑
    print(f"\n❌ 当前错误的读取逻辑（从basic_info读取）:")
    basic_info = ai_export_data["team_info"]["ai_extracted_info"]["basic_info"]
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        value = basic_info.get(color_field, "MISSING")
        print(f"   {color_field}: '{value}'")
    
    # 3. 正确的读取逻辑
    print(f"\n✅ 正确的读取逻辑（从kit_colors读取）:")
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        value = kit_colors.get(color_field, "MISSING")
        print(f"   {color_field}: '{value}'")
    
    return ai_export_data

def test_fashion_workflow_service_color_merge():
    """测试fashion_workflow_service中的颜色合并逻辑"""
    print("\n" + "=" * 60)
    print("🔍 测试fashion_workflow_service颜色合并逻辑")
    print("=" * 60)
    
    # 模拟当前的合并逻辑
    ai_export_data = test_color_field_data_flow()
    ai_extracted_info = ai_export_data["team_info"]["ai_extracted_info"]
    basic_info = ai_extracted_info.get("basic_info", {})
    kit_colors = ai_extracted_info.get("kit_colors", {})
    
    team_data = {"name": "颜色测试队"}
    
    def is_valid_value(value):
        """检查值是否有效（不是占位符）"""
        if not value or value in ["待定", "未知", "暂无", ""]:
            return False
        return True
    
    print(f"\n❌ 当前错误的合并逻辑:")
    # 当前的错误逻辑（从basic_info读取）
    if is_valid_value(basic_info.get("jersey_color")):
        team_data["jersey_color"] = basic_info.get("jersey_color")
        print(f"   设置jersey_color: '{basic_info.get('jersey_color')}'")
    else:
        print(f"   jersey_color未设置（从basic_info中未找到）")
    
    if is_valid_value(basic_info.get("shorts_color")):
        team_data["shorts_color"] = basic_info.get("shorts_color")
        print(f"   设置shorts_color: '{basic_info.get('shorts_color')}'")
    else:
        print(f"   shorts_color未设置（从basic_info中未找到）")
    
    print(f"\n✅ 正确的合并逻辑:")
    # 正确的逻辑（从kit_colors读取）
    team_data_correct = {"name": "颜色测试队"}
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if is_valid_value(kit_colors.get(color_field)):
            team_data_correct[color_field] = kit_colors.get(color_field)
            print(f"   设置{color_field}: '{kit_colors.get(color_field)}'")
        else:
            print(f"   {color_field}未设置")
    
    return team_data_correct

def test_word_generator_service_mapping():
    """测试WordGeneratorService中的字段映射"""
    print("\n" + "=" * 60)
    print("🔍 测试WordGeneratorService字段映射")
    print("=" * 60)
    
    # 使用正确合并后的team_data
    team_data = test_fashion_workflow_service_color_merge()
    
    # 模拟WordGeneratorService的_prepare_json_data逻辑
    team_name = team_data.get('name', '足球队')
    
    team_info = {
        "title": f"{team_name}报名表",
        "organizationName": team_name,
        "teamLeader": team_data.get('leader', ''),
        "coach": team_data.get('coach', ''),
        "teamDoctor": team_data.get('doctor', ''),
        "contactPerson": team_data.get('contact_person', team_data.get('leader', '')),
        "contactPhone": team_data.get('contact_phone', ''),
        # 颜色字段映射
        "jerseyColor": team_data.get('jersey_color', ''),
        "shortsColor": team_data.get('shorts_color', ''),
        "socksColor": team_data.get('socks_color', ''),
        "goalkeeperKitColor": team_data.get('goalkeeper_kit_color', '')
    }
    
    print(f"\n📄 WordGeneratorService映射后的数据:")
    for field, value in team_info.items():
        if 'Color' in field or field in ['contactPerson', 'contactPhone']:
            status = "✅" if value else "❌"
            print(f"   {status} {field}: '{value}'")
    
    return team_info

def test_java_data_structure():
    """测试Java程序接收的数据结构"""
    print("\n" + "=" * 60)
    print("🔍 测试Java程序数据结构")
    print("=" * 60)
    
    team_info = test_word_generator_service_mapping()
    
    # 模拟完整的JSON数据结构
    json_data = {
        "teamInfo": team_info,
        "players": [
            {
                "number": "1",
                "name": "测试球员1",
                "photoPath": "/path/to/photo1.jpg"
            }
        ],
        "config": {
            "templatePath": "/path/to/template.docx",
            "outputDir": "/path/to/output",
            "photosDir": "/path/to/photos"
        }
    }
    
    print(f"\n📄 发送给Java程序的完整数据结构:")
    print(f"teamInfo字段数量: {len(json_data['teamInfo'])}")
    
    # 检查关键字段
    critical_fields = [
        'jerseyColor', 'shortsColor', 'socksColor', 'goalkeeperKitColor',
        'contactPerson', 'contactPhone', 'teamLeader', 'coach', 'teamDoctor'
    ]
    
    print(f"\n🔍 关键字段检查:")
    for field in critical_fields:
        value = json_data['teamInfo'].get(field, 'MISSING')
        status = "✅" if value and value != 'MISSING' else "❌"
        print(f"   {status} {field}: '{value}'")
    
    return json_data

def create_test_json_file():
    """创建测试用的JSON文件"""
    print("\n" + "=" * 60)
    print("🔍 创建测试JSON文件")
    print("=" * 60)
    
    json_data = test_java_data_structure()
    
    # 写入临时文件
    with tempfile.NamedTemporaryFile(
        mode='w', 
        suffix='.json', 
        delete=False, 
        encoding='utf-8',
        prefix='missing_fields_test_'
    ) as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
        temp_file = f.name
    
    print(f"✅ 测试JSON文件已创建: {temp_file}")
    
    # 显示文件内容摘要
    print(f"\n📄 文件内容摘要:")
    with open(temp_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(f"   文件大小: {len(content)} 字符")
        print(f"   包含jerseyColor: {'jerseyColor' in content}")
        print(f"   包含shortsColor: {'shortsColor' in content}")
        print(f"   包含socksColor: {'socksColor' in content}")
        print(f"   包含goalkeeperKitColor: {'goalkeeperKitColor' in content}")
    
    return temp_file

def main():
    """主测试函数"""
    print("🚀 开始全面测试Word生成中的缺失字段")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 执行所有测试
        test_json_file = create_test_json_file()
        
        print("\n" + "=" * 60)
        print("📋 测试总结")
        print("=" * 60)
        
        print("✅ 发现的主要问题:")
        print("   1. 颜色字段从错误位置读取（basic_info而非kit_colors）")
        print("   2. 需要修复fashion_workflow_service.py中的合并逻辑")
        print("   3. WordGeneratorService映射逻辑正确")
        print("   4. Java数据结构正确")
        
        print(f"\n📄 测试JSON文件: {test_json_file}")
        print("   可以用此文件直接测试Java程序")
        
        # 清理临时文件
        if os.path.exists(test_json_file):
            os.unlink(test_json_file)
            print(f"   已清理临时文件")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
