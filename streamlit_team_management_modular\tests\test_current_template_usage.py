#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前系统使用的模板文件
"""

import os
import json
import zipfile
import xml.etree.ElementTree as ET
import re

def check_available_templates():
    """检查可用的模板文件"""
    print("🔍 检查可用的模板文件")
    print("=" * 60)
    
    template_dir = "../word_zc"
    
    if not os.path.exists(template_dir):
        print(f"❌ 模板目录不存在: {template_dir}")
        return []
    
    # 查找所有模板文件
    template_files = []
    for file in os.listdir(template_dir):
        if file.endswith('.docx') and 'template' in file.lower():
            template_files.append(file)
    
    print(f"📄 找到的模板文件:")
    for i, template in enumerate(template_files, 1):
        template_path = os.path.join(template_dir, template)
        file_size = os.path.getsize(template_path)
        mod_time = os.path.getmtime(template_path)
        
        print(f"   {i}. {template}")
        print(f"      路径: {template_path}")
        print(f"      大小: {file_size} bytes")
        print(f"      修改时间: {mod_time}")
        
        # 检查是否是我们创建的修复版本
        if 'fixed' in template.lower():
            print(f"      类型: 🔧 修复后的模板")
        elif 'backup' in template.lower():
            print(f"      类型: 💾 备份模板")
        else:
            print(f"      类型: 📄 原始模板")
        print()
    
    return template_files

def check_current_configuration():
    """检查当前配置使用的模板"""
    print("🔍 检查当前配置使用的模板")
    print("=" * 60)
    
    try:
        from config.settings import app_settings
        
        print("📄 配置信息:")
        
        # 检查word_generator配置
        if hasattr(app_settings, 'word_generator'):
            word_config = app_settings.word_generator
            print(f"   word_generator配置存在: ✅")
            
            # 获取模板路径配置
            if hasattr(word_config, 'template_filename'):
                template_filename = word_config.template_filename
                print(f"   配置的模板文件名: '{template_filename}'")
            else:
                print(f"   ❌ 未找到template_filename配置")
            
            # 检查路径配置
            if hasattr(app_settings, 'paths'):
                paths_config = app_settings.paths
                if hasattr(paths_config, 'word_template_dir'):
                    template_dir = paths_config.word_template_dir
                    print(f"   配置的模板目录: '{template_dir}'")
                    
                    # 构建完整路径
                    if 'template_filename' in locals():
                        full_template_path = os.path.join(template_dir, template_filename)
                        print(f"   完整模板路径: '{full_template_path}'")
                        
                        # 检查文件是否存在
                        if os.path.exists(full_template_path):
                            print(f"   模板文件存在: ✅")
                            return full_template_path
                        else:
                            print(f"   模板文件不存在: ❌")
                else:
                    print(f"   ❌ 未找到word_template_dir配置")
        else:
            print(f"   ❌ 未找到word_generator配置")
        
        return None
        
    except Exception as e:
        print(f"❌ 检查配置失败: {e}")
        return None

def test_actual_word_generation():
    """测试实际的Word生成过程"""
    print(f"\n🔍 测试实际的Word生成过程")
    print("=" * 60)
    
    try:
        import streamlit as st
        
        # 设置session state
        if 'user_id' not in st.session_state:
            st.session_state.user_id = 'user_c61aa17e3868'
        
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建工作流服务
        workflow_service = FashionWorkflowService('user_c61aa17e3868')
        
        print("📄 执行Word生成...")
        
        # 生成Word文档
        word_result = workflow_service._auto_generate_word_document(
            "天依369", {}, None
        )
        
        if word_result.get('success'):
            print("✅ Word生成成功")
            file_path = word_result.get('file_path')
            print(f"   生成文件: {os.path.basename(file_path)}")
            
            # 分析使用的模板信息
            return analyze_generated_word_for_template_info(file_path)
        else:
            print(f"❌ Word生成失败: {word_result.get('error', 'Unknown error')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_word_for_template_info(file_path):
    """分析生成的Word文档来推断使用的模板"""
    print(f"\n🔍 分析生成的Word文档")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print(f"📄 生成的Word内容分析:")
                    
                    # 检查关键指标来判断使用的模板
                    analysis = {}
                    
                    # 1. 检查颜色字段是否正常工作
                    color_areas = []
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if any(keyword in word for keyword in ['球衣', '球裤', '球袜', '守门员']):
                            start = max(0, i-2)
                            end = min(len(words), i+8)
                            context = ' '.join(words[start:end])
                            color_areas.append(context)
                    
                    print(f"   颜色相关区域: {len(color_areas)}个")
                    for area in color_areas:
                        print(f"     {area}")
                    
                    # 2. 检查是否有未替换的占位符
                    remaining_placeholders = re.findall(r'\{\{[^}]+\}\}', content)
                    unique_remaining = list(set(remaining_placeholders))
                    
                    analysis['remaining_placeholders'] = unique_remaining
                    analysis['color_areas'] = color_areas
                    
                    print(f"\n   未替换占位符: {len(unique_remaining)}个")
                    for placeholder in unique_remaining:
                        print(f"     {placeholder}")
                    
                    # 3. 检查人员信息是否正确
                    has_auto_fill = "自动填充" in full_text
                    zhao_liu_count = full_text.count("赵六")
                    
                    analysis['has_auto_fill'] = has_auto_fill
                    analysis['zhao_liu_count'] = zhao_liu_count
                    
                    print(f"\n   人员信息分析:")
                    print(f"     '自动填充'字样: {'❌ 有' if has_auto_fill else '✅ 无'}")
                    print(f"     '赵六'出现次数: {zhao_liu_count}")
                    
                    return analysis
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

def check_wordgenerator_service_template():
    """检查WordGeneratorService实际使用的模板"""
    print(f"\n🔍 检查WordGeneratorService实际使用的模板")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 模拟创建WordGeneratorService
        paths = app_settings.word_generator.get_absolute_paths("test", app_settings.paths)
        
        print(f"📄 WordGeneratorService路径配置:")
        print(f"   jar_path: {paths.get('jar_path', 'MISSING')}")
        print(f"   template_path: {paths.get('template_path', 'MISSING')}")
        print(f"   output_dir: {paths.get('output_dir', 'MISSING')}")
        
        template_path = paths.get('template_path')
        if template_path:
            print(f"\n📄 实际使用的模板文件:")
            print(f"   路径: {template_path}")
            
            if os.path.exists(template_path):
                print(f"   存在: ✅")
                file_size = os.path.getsize(template_path)
                print(f"   大小: {file_size} bytes")
                
                # 检查文件名
                template_filename = os.path.basename(template_path)
                print(f"   文件名: {template_filename}")
                
                if 'fixed' in template_filename.lower():
                    print(f"   类型: 🔧 修复后的模板")
                    return 'fixed_template'
                else:
                    print(f"   类型: 📄 原始模板")
                    return 'original_template'
            else:
                print(f"   存在: ❌")
                return 'missing_template'
        else:
            print(f"   ❌ 未获取到模板路径")
            return 'no_path'
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None

def compare_template_contents():
    """比较两个模板的内容差异"""
    print(f"\n🔍 比较模板内容差异")
    print("=" * 60)
    
    original_template = "../word_zc/template_15players_fixed.docx"
    fixed_template = "../word_zc/template_15players_fixed.docx"
    
    templates_info = {}
    
    for template_name, template_path in [
        ("原始模板", original_template),
        ("修复模板", fixed_template)
    ]:
        print(f"\n📄 分析 {template_name}:")
        
        if not os.path.exists(template_path):
            print(f"   ❌ 文件不存在: {template_path}")
            continue
        
        try:
            with zipfile.ZipFile(template_path, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
                    
                    # 检查颜色占位符
                    color_placeholders = []
                    expected_placeholders = [
                        '{{jerseyColor}}',
                        '{{shortsColor}}',
                        '{{socksColor}}',
                        '{{goalkeeperKitColor}}'
                    ]
                    
                    for placeholder in expected_placeholders:
                        if placeholder in content:
                            color_placeholders.append(placeholder)
                    
                    # 检查分割的占位符
                    split_patterns = [
                        r'jerseyColor.*?spellEnd',
                        r'shortsColor.*?spellEnd',
                        r'socksColor.*?spellEnd',
                        r'goalkeeperKitColor.*?spellEnd'
                    ]
                    
                    split_placeholders = []
                    for pattern in split_patterns:
                        matches = re.findall(pattern, content)
                        if matches:
                            split_placeholders.extend(matches)
                    
                    templates_info[template_name] = {
                        'color_placeholders': color_placeholders,
                        'split_placeholders': split_placeholders,
                        'file_size': os.path.getsize(template_path)
                    }
                    
                    print(f"   完整颜色占位符: {len(color_placeholders)}个")
                    for placeholder in color_placeholders:
                        print(f"     ✅ {placeholder}")
                    
                    print(f"   分割的占位符: {len(split_placeholders)}个")
                    for placeholder in split_placeholders[:3]:  # 只显示前3个
                        print(f"     ❌ {placeholder[:50]}...")
                    
                    print(f"   文件大小: {os.path.getsize(template_path)} bytes")
                    
        except Exception as e:
            print(f"   ❌ 分析失败: {e}")
    
    return templates_info

def main():
    """主函数"""
    print("🎯 测试当前系统使用的模板文件")
    print("=" * 70)
    print("检查配置、实际使用情况和模板差异")
    print("=" * 70)
    
    # 1. 检查可用的模板文件
    available_templates = check_available_templates()
    
    # 2. 检查当前配置
    config_template = check_current_configuration()
    
    # 3. 检查WordGeneratorService实际使用的模板
    service_template = check_wordgenerator_service_template()
    
    # 4. 测试实际Word生成
    generation_analysis = test_actual_word_generation()
    
    # 5. 比较模板内容
    template_comparison = compare_template_contents()
    
    # 综合分析
    print(f"\n📊 综合分析结果")
    print("=" * 70)
    
    print(f"🔍 模板使用情况:")
    print(f"   可用模板: {len(available_templates)}个")
    for template in available_templates:
        print(f"     • {template}")
    
    if config_template:
        print(f"   配置指向: {os.path.basename(config_template)}")
    else:
        print(f"   配置指向: ❌ 未找到")
    
    if service_template:
        print(f"   服务使用: {service_template}")
    else:
        print(f"   服务使用: ❌ 未确定")
    
    # 根据生成结果推断使用的模板
    if generation_analysis:
        print(f"\n🔍 根据生成结果推断:")
        
        remaining_placeholders = generation_analysis.get('remaining_placeholders', [])
        has_auto_fill = generation_analysis.get('has_auto_fill', False)
        zhao_liu_count = generation_analysis.get('zhao_liu_count', 0)
        
        if len(remaining_placeholders) == 0 and not has_auto_fill and zhao_liu_count >= 4:
            print(f"   ✅ 系统工作正常，可能使用修复后的模板或修复生效")
        elif len(remaining_placeholders) > 0:
            print(f"   ⚠️ 有未替换占位符，可能使用原始模板")
        elif has_auto_fill:
            print(f"   ⚠️ 有'自动填充'字样，自动填充逻辑可能有问题")
        else:
            print(f"   ❓ 状态不明确")
    
    # 模板差异分析
    if template_comparison:
        print(f"\n🔍 模板差异分析:")
        for template_name, info in template_comparison.items():
            color_count = len(info['color_placeholders'])
            split_count = len(info['split_placeholders'])
            
            if color_count == 4 and split_count == 0:
                print(f"   {template_name}: ✅ 完美 (4个完整颜色占位符)")
            elif color_count > 0:
                print(f"   {template_name}: ⚠️ 部分修复 ({color_count}个完整占位符)")
            else:
                print(f"   {template_name}: ❌ 需要修复 (0个完整占位符)")
    
    print(f"\n🎯 结论:")
    print(f"   基于以上分析，当前系统使用的模板状态和类型已确定")
    print(f"   如果Word生成正常工作，说明使用的是修复后的版本或修复已生效")

if __name__ == "__main__":
    main()
