#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证联系人信息修复效果
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET

def extract_word_content(docx_path):
    """提取Word文档的文本内容"""
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                tree = ET.parse(xml_file)
                root = tree.getroot()
                
                text_content = []
                for elem in root.iter():
                    if elem.text:
                        text_content.append(elem.text)
                
                return ' '.join(text_content)
    except Exception as e:
        print(f"❌ 提取Word内容失败: {e}")
        return None

def test_java_fix_verification():
    """测试Java修复后的效果"""
    print("🧪 测试Java修复后的联系人信息显示")
    print("=" * 60)
    
    # 创建包含联系人信息的测试数据
    test_data = {
        "teamInfo": {
            "title": "修复验证报名表",
            "organizationName": "修复验证队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "赵六",
            "contactPhone": "13800138000"
        },
        "players": [
            {
                "number": "10",
                "name": "验证球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入测试文件
        test_file = "final_fix_verification.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件: {test_file}")
        print("📄 测试数据:")
        print(f"   contactPerson: '{test_data['teamInfo']['contactPerson']}'")
        print(f"   contactPhone: '{test_data['teamInfo']['contactPhone']}'")
        
        # 运行Java程序
        print("\n🚀 运行修复后的Java程序...")
        result = subprocess.run(
            ["java", "-cp", "target/word-generator.jar", "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        print(f"📊 返回码: {result.returncode}")
        
        if result.stderr:
            print("\n📝 Java日志输出:")
            for line in result.stderr.split('\n'):
                if 'INFO:Team info parsed:' in line:
                    print(f"   🔍 团队信息解析: {line}")
                    
                    # 检查联系人信息是否正确解析
                    if '联系人=赵六' in line and '联系电话=13800138000' in line:
                        print("   ✅ 联系人信息解析成功！")
                    elif '联系人=' in line and '联系电话=' in line:
                        print("   ⚠️ 联系人字段存在但值可能为空")
                    else:
                        print("   ❌ 联系人字段缺失")
        
        if result.returncode == 0:
            print("\n✅ Java程序运行成功")
            
            # 查找生成的文件
            output_dir = "output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 提取Word内容并检查联系人信息
                    content = extract_word_content(latest_file)
                    if content:
                        print("\n📝 检查Word文档内容:")
                        
                        # 检查联系人信息是否在文档中
                        has_contact_person = "赵六" in content
                        has_contact_phone = "13800138000" in content
                        has_placeholder_person = "{{contactPerson}}" in content
                        has_placeholder_phone = "{{contactPhone}}" in content
                        
                        print(f"   联系人'赵六'在文档中: {'✅ 是' if has_contact_person else '❌ 否'}")
                        print(f"   电话'13800138000'在文档中: {'✅ 是' if has_contact_phone else '❌ 否'}")
                        print(f"   占位符{{{{contactPerson}}}}仍存在: {'⚠️ 是' if has_placeholder_person else '✅ 否'}")
                        print(f"   占位符{{{{contactPhone}}}}仍存在: {'⚠️ 是' if has_placeholder_phone else '✅ 否'}")
                        
                        # 显示包含联系人信息的内容片段
                        if has_contact_person or has_contact_phone:
                            print(f"\n📄 联系人相关内容片段:")
                            lines = content.split()
                            for i, word in enumerate(lines):
                                if "赵六" in word or "13800138000" in word:
                                    start = max(0, i-5)
                                    end = min(len(lines), i+6)
                                    context = ' '.join(lines[start:end])
                                    print(f"   ...{context}...")
                                    break
                        
                        return {
                            'success': True,
                            'has_contact_person': has_contact_person,
                            'has_contact_phone': has_contact_phone,
                            'has_placeholder_person': has_placeholder_person,
                            'has_placeholder_phone': has_placeholder_phone,
                            'file_path': latest_file
                        }
                    else:
                        print("❌ 无法提取Word内容")
                        return {'success': False, 'error': '无法提取Word内容'}
        else:
            print(f"❌ Java程序运行失败: {result.returncode}")
            if result.stderr:
                print("错误信息:")
                print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            return {'success': False, 'error': f'Java程序失败: {result.returncode}'}
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return {'success': False, 'error': str(e)}
    finally:
        # 清理测试文件
        if os.path.exists("final_fix_verification.json"):
            os.remove("final_fix_verification.json")

def test_python_integration_fix():
    """测试Python集成的修复效果"""
    print("\n🧪 测试Python集成的联系人信息显示")
    print("=" * 60)
    
    try:
        # 切换到正确的目录
        os.chdir("../streamlit_team_management_modular")
        
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 准备测试数据
        team_data = {
            'name': 'Python集成修复验证队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '验证球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print("📄 测试数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 生成Word文档
        print("\n🚀 运行Python集成...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Python集成成功: {result['file_path']}")
            
            # 提取Word内容并检查联系人信息
            content = extract_word_content(result['file_path'])
            if content:
                print("\n📝 检查Word文档内容:")
                
                # 检查联系人信息是否在文档中
                has_contact_person = "赵六" in content
                has_contact_phone = "13800138000" in content
                
                print(f"   联系人'赵六'在文档中: {'✅ 是' if has_contact_person else '❌ 否'}")
                print(f"   电话'13800138000'在文档中: {'✅ 是' if has_contact_phone else '❌ 否'}")
                
                return {
                    'success': True,
                    'has_contact_person': has_contact_person,
                    'has_contact_phone': has_contact_phone,
                    'file_path': result['file_path']
                }
            else:
                print("❌ 无法提取Word内容")
                return {'success': False, 'error': '无法提取Word内容'}
        else:
            print(f"❌ Python集成失败: {result['message']}")
            return {'success': False, 'error': result['message']}
            
    except Exception as e:
        print(f"❌ Python集成测试失败: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🎯 最终联系人信息修复验证")
    print("=" * 70)
    
    # 测试1: Java修复验证
    java_result = test_java_fix_verification()
    
    # 测试2: Python集成验证
    python_result = test_python_integration_fix()
    
    # 分析结果
    print("\n📊 修复验证结果")
    print("=" * 70)
    
    print("🔍 Java修复效果:")
    if java_result.get('success'):
        if java_result.get('has_contact_person') and java_result.get('has_contact_phone'):
            print("   ✅ 联系人信息正确显示在Word文档中")
        else:
            print("   ❌ 联系人信息仍未显示在Word文档中")
        
        if not java_result.get('has_placeholder_person') and not java_result.get('has_placeholder_phone'):
            print("   ✅ 占位符已被正确替换")
        else:
            print("   ⚠️ 占位符未完全替换")
    else:
        print("   ❌ Java程序运行失败")
    
    print("\n🔍 Python集成效果:")
    if python_result.get('success'):
        if python_result.get('has_contact_person') and python_result.get('has_contact_phone'):
            print("   ✅ 联系人信息正确显示在Word文档中")
        else:
            print("   ❌ 联系人信息仍未显示在Word文档中")
    else:
        print("   ❌ Python集成失败")
    
    # 最终结论
    print("\n🎯 最终结论:")
    
    java_works = java_result.get('success') and java_result.get('has_contact_person') and java_result.get('has_contact_phone')
    python_works = python_result.get('success') and python_result.get('has_contact_person') and python_result.get('has_contact_phone')
    
    if java_works and python_works:
        print("🎉 修复完全成功！")
        print("✅ Java和Python两种方式都能正确显示联系人信息")
        print("✅ 用户在AI聊天中输入联系人和电话后，系统会自动保存并传入Word模板")
        print("✅ 联系人信息流程已完整实现")
    elif python_works:
        print("✅ Python集成修复成功！")
        print("✅ Streamlit应用中的联系人信息流程已完整")
        print("⚠️ Java直接调用可能还有问题，但不影响主要功能")
    elif java_works:
        print("✅ Java修复成功！")
        print("⚠️ Python集成可能还有问题，需要进一步检查")
    else:
        print("❌ 修复未完全成功")
        print("💡 需要进一步调试和修复")
    
    print("\n💡 用户使用指南:")
    if python_works:
        print("1. 在AI聊天中输入: '我是张三，电话13800138000'")
        print("2. AI会自动提取并保存联系人信息")
        print("3. 生成Word报名表时，联系人信息会自动填入")
        print("4. 最终的Word文档包含完整的联系人信息")

if __name__ == "__main__":
    main()
