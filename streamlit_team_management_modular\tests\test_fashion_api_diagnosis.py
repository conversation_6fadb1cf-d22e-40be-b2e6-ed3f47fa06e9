#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
换装API诊断测试脚本
专门用于诊断和测试换装API的各种问题
"""

import requests
import os
import time
import ssl
import urllib3
from urllib3.exceptions import InsecureRequestWarning
import json
from pathlib import Path
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from config.settings import app_settings

class FashionAPIDiagnosis:
    """换装API诊断类"""
    
    def __init__(self):
        self.api_key = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
        self.base_url = "https://api.302.ai"
        self.test_results = {}
        
    def test_basic_connectivity(self):
        """测试基本连接性"""
        print("🌐 测试基本连接性...")
        print("=" * 50)
        
        try:
            # 测试基本HTTP连接
            response = requests.get(self.base_url, timeout=10)
            print(f"✅ 基本连接成功，状态码: {response.status_code}")
            self.test_results["basic_connectivity"] = True
            return True
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL错误: {e}")
            self.test_results["basic_connectivity"] = False
            self.test_results["ssl_error"] = str(e)
            return False
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            self.test_results["basic_connectivity"] = False
            self.test_results["connection_error"] = str(e)
            return False
    
    def test_ssl_configurations(self):
        """测试不同的SSL配置"""
        print("\n🔒 测试SSL配置...")
        print("=" * 50)
        
        # 测试1: 禁用SSL验证
        try:
            urllib3.disable_warnings(InsecureRequestWarning)
            response = requests.get(self.base_url, verify=False, timeout=10)
            print(f"✅ 禁用SSL验证成功，状态码: {response.status_code}")
            self.test_results["ssl_disabled"] = True
        except Exception as e:
            print(f"❌ 禁用SSL验证失败: {e}")
            self.test_results["ssl_disabled"] = False
        
        # 测试2: 使用不同的SSL上下文
        try:
            session = requests.Session()
            session.mount('https://', requests.adapters.HTTPAdapter(
                max_retries=urllib3.util.Retry(total=3, backoff_factor=1)
            ))
            response = session.get(self.base_url, timeout=10)
            print(f"✅ 自定义SSL适配器成功，状态码: {response.status_code}")
            self.test_results["custom_ssl_adapter"] = True
        except Exception as e:
            print(f"❌ 自定义SSL适配器失败: {e}")
            self.test_results["custom_ssl_adapter"] = False
    
    def test_api_endpoints(self):
        """测试具体的API端点"""
        print("\n🎯 测试API端点...")
        print("=" * 50)
        
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        # 测试换装API端点
        fashion_url = f"{self.base_url}/302/comfyui/clothes-changer/create-task"
        try:
            # 只测试端点可达性，不发送实际数据
            response = requests.options(fashion_url, headers=headers, timeout=10)
            print(f"✅ 换装API端点可达，状态码: {response.status_code}")
            self.test_results["fashion_endpoint"] = True
        except Exception as e:
            print(f"❌ 换装API端点失败: {e}")
            self.test_results["fashion_endpoint"] = False
        
        # 测试背景移除API端点
        bg_remove_url = f"{self.base_url}/clipdrop/remove-background/v1"
        try:
            response = requests.options(bg_remove_url, headers=headers, timeout=10)
            print(f"✅ 背景移除API端点可达，状态码: {response.status_code}")
            self.test_results["bg_remove_endpoint"] = True
        except Exception as e:
            print(f"❌ 背景移除API端点失败: {e}")
            self.test_results["bg_remove_endpoint"] = False
            self.test_results["bg_remove_error"] = str(e)
    
    def test_with_actual_image(self):
        """使用实际图片测试背景移除API"""
        print("\n📸 测试实际图片处理...")
        print("=" * 50)
        
        # 查找测试图片
        test_image_path = None
        possible_paths = [
            "photos/player1_cropped.png",
            "photos/player2_cropped.jpg",
            "photos/player3_cropped.jpg",
            "data/user_a13da2c47ed7/uploads/测试AI感知/8b4bfbb0be374765a2884b8771dc1232.jpg"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                test_image_path = path
                break
        
        if not test_image_path:
            print("❌ 未找到测试图片")
            self.test_results["actual_image_test"] = False
            return
        
        print(f"📁 使用测试图片: {test_image_path}")
        
        # 测试背景移除API
        url = f"{self.base_url}/clipdrop/remove-background/v1"
        headers = {"Authorization": f"Bearer {self.api_key}"}
        
        try:
            with open(test_image_path, 'rb') as image_file:
                files = {'image_file': (os.path.basename(test_image_path), image_file, 'image/jpeg')}
                
                print("🚀 发送背景移除请求...")
                response = requests.post(
                    url, 
                    headers=headers, 
                    files=files, 
                    timeout=30,
                    verify=False  # 临时禁用SSL验证进行测试
                )
                
                print(f"📡 响应状态码: {response.status_code}")
                print(f"📋 响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    print("✅ 背景移除API调用成功！")
                    self.test_results["actual_image_test"] = True
                    
                    # 保存结果
                    output_path = "test_bg_remove_result.png"
                    with open(output_path, 'wb') as f:
                        f.write(response.content)
                    print(f"💾 结果已保存到: {output_path}")
                    
                else:
                    print(f"❌ API调用失败，状态码: {response.status_code}")
                    print(f"📄 响应内容: {response.text}")
                    self.test_results["actual_image_test"] = False
                    self.test_results["api_error_response"] = response.text
                    
        except requests.exceptions.SSLError as e:
            print(f"❌ SSL错误: {e}")
            self.test_results["actual_image_test"] = False
            self.test_results["ssl_error_detail"] = str(e)
        except Exception as e:
            print(f"❌ 请求失败: {e}")
            self.test_results["actual_image_test"] = False
            self.test_results["request_error"] = str(e)
    
    def test_network_environment(self):
        """测试网络环境"""
        print("\n🌍 测试网络环境...")
        print("=" * 50)
        
        # 测试DNS解析
        try:
            import socket
            ip = socket.gethostbyname("api.302.ai")
            print(f"✅ DNS解析成功: api.302.ai -> {ip}")
            self.test_results["dns_resolution"] = True
        except Exception as e:
            print(f"❌ DNS解析失败: {e}")
            self.test_results["dns_resolution"] = False
        
        # 测试代理设置
        proxies = os.environ.get('HTTP_PROXY') or os.environ.get('HTTPS_PROXY')
        if proxies:
            print(f"⚠️ 检测到代理设置: {proxies}")
            self.test_results["proxy_detected"] = True
        else:
            print("✅ 未检测到代理设置")
            self.test_results["proxy_detected"] = False
    
    def generate_diagnosis_report(self):
        """生成诊断报告"""
        print("\n📊 诊断报告")
        print("=" * 50)
        
        print("🔍 测试结果汇总:")
        for key, value in self.test_results.items():
            status = "✅" if value else "❌"
            print(f"  {status} {key}: {value}")
        
        print("\n💡 问题分析和建议:")
        
        if not self.test_results.get("basic_connectivity", False):
            print("❌ 基本连接失败 - 可能是网络问题或防火墙阻止")
        
        if "ssl_error" in self.test_results:
            print("❌ SSL证书问题 - 建议:")
            print("   1. 检查系统时间是否正确")
            print("   2. 更新CA证书")
            print("   3. 临时禁用SSL验证进行测试")
        
        if not self.test_results.get("bg_remove_endpoint", False):
            print("❌ 背景移除API端点不可达 - 可能原因:")
            print("   1. API服务暂时不可用")
            print("   2. API密钥权限不足")
            print("   3. 网络连接问题")
        
        if self.test_results.get("proxy_detected", False):
            print("⚠️ 检测到代理设置 - 可能影响SSL连接")
        
        # 保存详细报告
        report_path = "fashion_api_diagnosis_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        print(f"\n📄 详细报告已保存到: {report_path}")

def main():
    """主函数"""
    print("🔧 换装API诊断测试开始")
    print("=" * 60)
    
    diagnosis = FashionAPIDiagnosis()
    
    # 执行所有测试
    diagnosis.test_basic_connectivity()
    diagnosis.test_ssl_configurations()
    diagnosis.test_network_environment()
    diagnosis.test_api_endpoints()
    diagnosis.test_with_actual_image()
    
    # 生成报告
    diagnosis.generate_diagnosis_report()
    
    print("\n🎯 诊断测试完成！")

if __name__ == "__main__":
    main()
