#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试联系人信息修复效果
"""

import sys
import os
import json

def test_fixed_data_mapping():
    """测试修复后的数据映射"""
    print("🔍 测试修复后的数据映射")
    print("=" * 60)
    
    # 模拟AI数据结构
    ai_data = {
        "team_info": {
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "803",
                    "contact_person": "赵六",
                    "contact_phone": "18454432036",
                    "leader_name": "赵六",
                    "coach_name": "待定",
                    "team_doctor": "待定"
                }
            }
        }
    }
    
    # 模拟修复后的数据映射逻辑
    team_name = "803"
    team_info = ai_data.get("team_info", {})
    ai_extracted_info = team_info.get("ai_extracted_info", {})
    basic_info = ai_extracted_info.get("basic_info", {})
    
    # 修复后的team_data
    team_data = {
        "name": team_name,
        "leader": basic_info.get("leader_name", ""),
        "coach": basic_info.get("coach_name", ""),
        "doctor": basic_info.get("team_doctor", ""),
        "contact_person": basic_info.get("contact_person", ""),
        "contact_phone": basic_info.get("contact_phone", "")
    }
    
    print("📄 修复后的team_data:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 测试Word生成服务的数据准备
    print(f"\n🔍 测试WordGeneratorService数据准备:")
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        paths = app_settings.word_generator.get_absolute_paths("fix_test", app_settings.paths)
        
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 模拟球员数据
        players_data = [
            {
                'name': '张三',
                'jersey_number': '1',
                'photo': 'data/user_b7edc3878c81/photos/803/5ca89f6456b34d09b462f161866a0ffe.jpg'
            }
        ]
        
        # 检查数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        team_info = json_data['teamInfo']
        
        print(f"📄 WordGeneratorService准备的数据:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        print(f"   teamLeader: '{team_info.get('teamLeader', 'MISSING')}'")
        print(f"   coach: '{team_info.get('coach', 'MISSING')}'")
        print(f"   teamDoctor: '{team_info.get('teamDoctor', 'MISSING')}'")
        
        # 生成Word文档测试
        print(f"\n🚀 测试Word文档生成:")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Word生成成功: {os.path.basename(result['file_path'])}")
            
            # 检查生成的文件内容
            return check_generated_word_content(result['file_path'])
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def check_generated_word_content(file_path):
    """检查生成的Word文档内容"""
    print(f"\n🔍 检查生成的Word文档内容")
    print("=" * 60)
    
    try:
        import zipfile
        import xml.etree.ElementTree as ET
        
        with zipfile.ZipFile(file_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 检查联系人信息
                    has_contact_person = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"📄 内容检查:")
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人上下文
                    if has_contact_person or has_contact_phone:
                        words = full_text.split()
                        for i, word in enumerate(words):
                            if "联系人" in word or "赵六" in word:
                                start = max(0, i-3)
                                end = min(len(words), i+15)
                                context = ' '.join(words[start:end])
                                print(f"   联系人上下文: {context}")
                                break
                    
                    return has_contact_person and has_contact_phone
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_streamlit_workflow_simulation():
    """模拟Streamlit工作流程"""
    print(f"\n🔍 模拟Streamlit工作流程")
    print("=" * 60)
    
    try:
        # 模拟从实际AI数据文件加载
        ai_data_file = "data/user_b7edc3878c81/enhanced_ai_data/803_ai_data.json"
        
        if not os.path.exists(ai_data_file):
            print(f"❌ AI数据文件不存在: {ai_data_file}")
            return False
        
        with open(ai_data_file, 'r', encoding='utf-8') as f:
            ai_chat_data = json.load(f)
        
        print("📄 实际AI数据:")
        extracted_info = ai_chat_data.get('extracted_info', {})
        basic_info = extracted_info.get('basic_info', {})
        
        print(f"   contact_person: '{basic_info.get('contact_person', 'MISSING')}'")
        print(f"   contact_phone: '{basic_info.get('contact_phone', 'MISSING')}'")
        print(f"   leader_name: '{basic_info.get('leader_name', 'MISSING')}'")
        
        # 模拟fashion_workflow.py中的数据转换
        # 这里需要模拟_convert_ai_chat_data_to_export_format的逻辑
        converted_data = {
            "team_info": {
                "ai_extracted_info": {
                    "basic_info": basic_info
                }
            }
        }
        
        # 应用修复后的数据映射
        team_name = "803"
        team_info = converted_data.get("team_info", {})
        ai_extracted_info = team_info.get("ai_extracted_info", {})
        basic_info = ai_extracted_info.get("basic_info", {})
        
        team_data = {
            "name": team_name,
            "leader": basic_info.get("leader_name", ""),
            "coach": basic_info.get("coach_name", ""),
            "doctor": basic_info.get("team_doctor", ""),
            "contact_person": basic_info.get("contact_person", ""),
            "contact_phone": basic_info.get("contact_phone", "")
        }
        
        print(f"\n📄 修复后的team_data:")
        for key, value in team_data.items():
            print(f"   {key}: '{value}'")
        
        # 检查关键字段
        has_contact_person = bool(team_data.get('contact_person'))
        has_contact_phone = bool(team_data.get('contact_phone'))
        
        print(f"\n📊 关键字段检查:")
        print(f"   contact_person存在: {'✅ 是' if has_contact_person else '❌ 否'}")
        print(f"   contact_phone存在: {'✅ 是' if has_contact_phone else '❌ 否'}")
        
        return has_contact_person and has_contact_phone
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 测试联系人信息修复效果")
    print("=" * 70)
    
    # 测试1: 修复后的数据映射
    mapping_test = test_fixed_data_mapping()
    
    # 测试2: 模拟Streamlit工作流程
    workflow_test = test_streamlit_workflow_simulation()
    
    # 综合结果
    print(f"\n📊 测试结果汇总")
    print("=" * 70)
    
    print(f"🔍 数据映射修复: {'✅ 成功' if mapping_test else '❌ 失败'}")
    print(f"🔍 工作流程模拟: {'✅ 成功' if workflow_test else '❌ 失败'}")
    
    if mapping_test and workflow_test:
        print(f"\n🎉 联系人信息修复完全成功！")
        print(f"✅ 数据映射逻辑已修复")
        print(f"✅ 联系人信息能够正确传递到Word生成服务")
        print(f"✅ 生成的Word文档包含正确的联系人信息")
        
        print(f"\n🎯 用户现在可以:")
        print(f"   1. 在AI聊天中输入联系人信息")
        print(f"   2. 系统自动提取并保存")
        print(f"   3. 在换装工作流中生成Word报名表")
        print(f"   4. Word文档正确显示联系人信息")
        
        print(f"\n💡 建议用户:")
        print(f"   1. 重新运行Streamlit应用")
        print(f"   2. 重新生成Word报名表")
        print(f"   3. 检查联系人信息是否正确显示")
        
    elif mapping_test:
        print(f"\n✅ 数据映射修复成功")
        print(f"⚠️ 但工作流程模拟有问题")
        print(f"💡 可能需要检查AI数据转换逻辑")
        
    else:
        print(f"\n❌ 修复仍有问题")
        print(f"💡 需要进一步调试")

if __name__ == "__main__":
    main()
