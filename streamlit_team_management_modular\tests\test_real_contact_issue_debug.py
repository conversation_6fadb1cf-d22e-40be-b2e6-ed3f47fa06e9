#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试真实的联系人信息问题
用户反馈：生成的Word中仍然显示{{contactPerson}}和{{contactPhone}}占位符
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET
import tempfile
import shutil

def test_actual_user_data():
    """测试用户实际的数据路径"""
    print("🔍 测试1: 检查用户实际数据")
    print("=" * 60)
    
    user_data_path = "data/user_9385dff9dbed"
    
    if os.path.exists(user_data_path):
        print(f"✅ 找到用户数据目录: {user_data_path}")
        
        # 查找Word输出文件
        word_output_path = os.path.join(user_data_path, "word_output")
        if os.path.exists(word_output_path):
            print(f"✅ 找到Word输出目录: {word_output_path}")
            
            # 列出最近的Word文件
            word_files = [f for f in os.listdir(word_output_path) if f.endswith('.docx')]
            if word_files:
                latest_word = max([os.path.join(word_output_path, f) for f in word_files], 
                                key=os.path.getmtime)
                print(f"📄 最新Word文件: {latest_word}")
                
                # 检查Word文件内容
                return analyze_word_file_content(latest_word)
            else:
                print("❌ Word输出目录中没有.docx文件")
        else:
            print("❌ 未找到Word输出目录")
    else:
        print("❌ 未找到用户数据目录")
    
    return None

def analyze_word_file_content(word_file_path):
    """分析Word文件的详细内容"""
    print(f"\n🔍 分析Word文件内容: {os.path.basename(word_file_path)}")
    print("=" * 60)
    
    try:
        with zipfile.ZipFile(word_file_path, 'r') as zip_file:
            # 读取document.xml
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                print("📄 搜索联系人相关内容:")
                
                # 搜索占位符
                if '{{contactPerson}}' in content:
                    print("   ❌ 找到未替换的 {{contactPerson}} 占位符")
                else:
                    print("   ✅ 未找到 {{contactPerson}} 占位符")
                
                if '{{contactPhone}}' in content:
                    print("   ❌ 找到未替换的 {{contactPhone}} 占位符")
                else:
                    print("   ✅ 未找到 {{contactPhone}} 占位符")
                
                # 搜索可能的联系人信息
                contact_patterns = ['联系人', '电话', '手机', 'contact', 'phone']
                for pattern in contact_patterns:
                    if pattern in content:
                        print(f"   ✅ 找到相关内容: {pattern}")
                
                # 提取所有文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    # 查找联系人相关的上下文
                    print(f"\n📄 联系人相关上下文:")
                    words = full_text.split()
                    for i, word in enumerate(words):
                        if '联系人' in word or 'contactPerson' in word:
                            start = max(0, i-5)
                            end = min(len(words), i+10)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                        
                        if '电话' in word or 'contactPhone' in word:
                            start = max(0, i-5)
                            end = min(len(words), i+10)
                            context = ' '.join(words[start:end])
                            print(f"   电话上下文: {context}")
                    
                    return {
                        'has_contact_placeholder': '{{contactPerson}}' in content,
                        'has_phone_placeholder': '{{contactPhone}}' in content,
                        'full_text': full_text[:1000]  # 前1000字符
                    }
                    
                except ET.ParseError as e:
                    print(f"   ⚠️ XML解析错误: {e}")
                    return None
                
    except Exception as e:
        print(f"❌ 分析Word文件失败: {e}")
        return None

def test_current_word_generation_process():
    """测试当前的Word生成过程"""
    print("\n🔍 测试2: 当前Word生成过程")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 模拟用户数据
        team_data = {
            'name': '联系人调试测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '调试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        print("📄 输入数据:")
        print(f"   contact_person: '{team_data['contact_person']}'")
        print(f"   contact_phone: '{team_data['contact_phone']}'")
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("debug_user", app_settings.paths)
        
        print(f"\n📄 配置路径:")
        print(f"   jar_path: {paths['jar_path']}")
        print(f"   template_path: {paths['template_path']}")
        print(f"   output_dir: {paths['output_dir']}")
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 检查JSON数据准备
        json_data = word_service._prepare_json_data(team_data, players_data)
        
        print(f"\n📄 准备的JSON数据:")
        print(f"   teamInfo: {json.dumps(json_data['teamInfo'], ensure_ascii=False, indent=2)}")
        
        # 检查关键字段
        team_info = json_data['teamInfo']
        print(f"\n🔍 关键字段检查:")
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        
        # 保存JSON文件用于调试
        debug_json_path = "debug_current_generation.json"
        with open(debug_json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 保存调试JSON: {debug_json_path}")
        
        # 生成Word文档
        print(f"\n🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Word生成成功: {result['file_path']}")
            
            # 分析生成的Word文件
            word_analysis = analyze_word_file_content(result['file_path'])
            
            if word_analysis:
                if word_analysis['has_contact_placeholder'] or word_analysis['has_phone_placeholder']:
                    print("❌ 生成的Word文件仍包含未替换的占位符")
                    print("💡 这确认了用户报告的问题")
                else:
                    print("✅ 生成的Word文件中占位符已被替换")
            
            return result['file_path']
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return None
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def test_java_execution_with_debug():
    """测试Java执行过程的详细调试"""
    print("\n🔍 测试3: Java执行详细调试")
    print("=" * 60)
    
    # 使用之前生成的调试JSON
    debug_json_path = "debug_current_generation.json"
    
    if not os.path.exists(debug_json_path):
        print("❌ 调试JSON文件不存在")
        return None
    
    try:
        print(f"📄 使用调试JSON: {debug_json_path}")
        
        # 显示JSON内容
        with open(debug_json_path, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        print(f"📄 JSON中的联系人信息:")
        team_info = json_data.get('teamInfo', {})
        print(f"   contactPerson: '{team_info.get('contactPerson', 'MISSING')}'")
        print(f"   contactPhone: '{team_info.get('contactPhone', 'MISSING')}'")
        
        # 运行Java程序
        print(f"\n🚀 运行Java程序...")
        
        # 确保在正确的目录中运行
        java_dir = "../word_zc/ai-football-generator"
        if os.path.exists(java_dir):
            # 复制JSON文件到Java目录
            java_json_path = os.path.join(java_dir, "debug_test.json")
            shutil.copy2(debug_json_path, java_json_path)
            
            # 运行Java程序
            result = subprocess.run(
                ["java", "-cp", "target/word-generator.jar", "CommandLineMain", "debug_test.json"],
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=30,
                errors='ignore',
                cwd=java_dir
            )
            
            print(f"📊 Java返回码: {result.returncode}")
            
            if result.stdout:
                print(f"\n📝 Java标准输出:")
                for line in result.stdout.split('\n'):
                    if line.strip():
                        print(f"   {line}")
            
            if result.stderr:
                print(f"\n📝 Java错误输出:")
                for line in result.stderr.split('\n'):
                    if line.strip():
                        print(f"   {line}")
                        
                        # 特别关注团队信息解析
                        if 'INFO:Team info parsed:' in line:
                            print(f"   🔍 关键: {line}")
                            if '联系人=' in line and '联系电话=' in line:
                                # 检查联系人信息是否为空
                                if '联系人=,' in line or '联系电话=,' in line:
                                    print(f"   ❌ 联系人信息为空！")
                                else:
                                    print(f"   ✅ 联系人信息不为空")
            
            # 检查生成的文件
            output_dir = os.path.join(java_dir, "output")
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    print(f"\n✅ Java生成文件: {latest_file}")
                    
                    # 分析Java生成的文件
                    java_analysis = analyze_word_file_content(latest_file)
                    
                    if java_analysis:
                        if java_analysis['has_contact_placeholder'] or java_analysis['has_phone_placeholder']:
                            print("❌ Java生成的文件仍包含占位符")
                        else:
                            print("✅ Java生成的文件占位符已替换")
                    
                    return latest_file
            
            # 清理
            if os.path.exists(java_json_path):
                os.remove(java_json_path)
        
        return None
        
    except Exception as e:
        print(f"❌ Java调试失败: {e}")
        return None

def compare_template_and_output():
    """比较模板和输出文件"""
    print("\n🔍 测试4: 比较模板和输出文件")
    print("=" * 60)
    
    template_path = "../word_zc/template_15players_fixed.docx"
    
    if os.path.exists(template_path):
        print(f"✅ 找到模板文件: {template_path}")
        
        # 分析模板
        template_analysis = analyze_word_file_content(template_path)
        
        if template_analysis:
            print(f"\n📄 模板分析结果:")
            print(f"   包含{{{{contactPerson}}}}占位符: {'是' if template_analysis['has_contact_placeholder'] else '否'}")
            print(f"   包含{{{{contactPhone}}}}占位符: {'是' if template_analysis['has_phone_placeholder'] else '否'}")
            
            if template_analysis['has_contact_placeholder'] and template_analysis['has_phone_placeholder']:
                print("✅ 模板包含联系人占位符")
            else:
                print("❌ 模板缺少联系人占位符")
                
            return template_analysis
    else:
        print(f"❌ 模板文件不存在: {template_path}")
    
    return None

def main():
    """主调试函数"""
    print("🎯 真实联系人信息问题深度调试")
    print("=" * 70)
    print("用户反馈：生成的Word中仍显示{{contactPerson}}和{{contactPhone}}占位符")
    print("=" * 70)
    
    # 测试1: 检查用户实际数据
    user_analysis = test_actual_user_data()
    
    # 测试2: 测试当前Word生成过程
    current_word_file = test_current_word_generation_process()
    
    # 测试3: Java执行详细调试
    java_word_file = test_java_execution_with_debug()
    
    # 测试4: 比较模板和输出
    template_analysis = compare_template_and_output()
    
    # 综合分析
    print("\n📊 问题根因分析")
    print("=" * 70)
    
    print("🔍 关键发现:")
    
    if template_analysis:
        if template_analysis['has_contact_placeholder']:
            print("1. ✅ 模板确实包含联系人占位符")
        else:
            print("1. ❌ 模板缺少联系人占位符")
    
    print("2. 🔍 数据流转检查:")
    print("   - Python层是否正确准备数据")
    print("   - Java层是否正确解析数据")
    print("   - 模板引擎是否正确替换占位符")
    
    print("\n🎯 可能的问题原因:")
    print("1. Java代码虽然接收到数据，但模板引擎没有正确替换")
    print("2. 占位符格式问题（XML分割导致识别失败）")
    print("3. 模板引擎配置问题")
    print("4. 数据传递到模板引擎时丢失")
    
    print("\n💡 下一步调试方向:")
    print("1. 检查poi-tl模板引擎的配置和使用")
    print("2. 验证数据是否正确传递到模板引擎")
    print("3. 检查占位符在XML中的实际格式")
    print("4. 测试简单的占位符替换")
    
    # 清理调试文件
    debug_files = ["debug_current_generation.json"]
    for file in debug_files:
        if os.path.exists(file):
            os.remove(file)

if __name__ == "__main__":
    main()
