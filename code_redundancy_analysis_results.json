{"duplicate_code": {"functions": {"main": ["streamlit_team_management_modular\\analyze_actual_word_problems.py", "streamlit_team_management_modular\\analyze_current_user_issues.py", "streamlit_team_management_modular\\analyze_template_placeholders.py", "streamlit_team_management_modular\\analyze_word_content.py", "streamlit_team_management_modular\\app.py", "streamlit_team_management_modular\\check_actual_word_content.py", "streamlit_team_management_modular\\check_all_templates.py", "streamlit_team_management_modular\\check_final_word_content.py", "streamlit_team_management_modular\\check_latest_word.py", "streamlit_team_management_modular\\check_newest_word.py", "streamlit_team_management_modular\\check_template_placeholders.py", "streamlit_team_management_modular\\cleanup_old_directories.py", "streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\comprehensive_template_test.py", "streamlit_team_management_modular\\comprehensive_word_test.py", "streamlit_team_management_modular\\create_clean_template.py", "streamlit_team_management_modular\\create_default_template.py", "streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\create_visual_example.py", "streamlit_team_management_modular\\debug_actual_workflow.py", "streamlit_team_management_modular\\debug_ai_export_data.py", "streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\debug_auto_fill_issue.py", "streamlit_team_management_modular\\debug_color_placeholders.py", "streamlit_team_management_modular\\debug_data_flow.py", "streamlit_team_management_modular\\debug_fashion_task_failure.py", "streamlit_team_management_modular\\debug_java_color_processing.py", "streamlit_team_management_modular\\debug_new_user_data.py", "streamlit_team_management_modular\\debug_team_service_data.py", "streamlit_team_management_modular\\debug_tianyi369_issue.py", "streamlit_team_management_modular\\debug_tianyi909_word_generation.py", "streamlit_team_management_modular\\debug_upload.py", "streamlit_team_management_modular\\demo_enhanced_integration.py", "streamlit_team_management_modular\\demo_loading_experience.py", "streamlit_team_management_modular\\final_comprehensive_test.py", "streamlit_team_management_modular\\final_contact_verification.py", "streamlit_team_management_modular\\final_debug_test.py", "streamlit_team_management_modular\\final_fix_tianyi369.py", "streamlit_team_management_modular\\final_template_verification.py", "streamlit_team_management_modular\\find_all_old_template_references.py", "streamlit_team_management_modular\\find_auto_fill_in_data.py", "streamlit_team_management_modular\\fix_auto_fill_complete.py", "streamlit_team_management_modular\\fix_auto_fill_data.py", "streamlit_team_management_modular\\fix_color_placeholders_in_template.py", "streamlit_team_management_modular\\fix_intelligent_matching_issue.py", "streamlit_team_management_modular\\fix_poi_tl_config.py", "streamlit_team_management_modular\\fix_template_placeholders.py", "streamlit_team_management_modular\\fix_word_generation.py", "streamlit_team_management_modular\\migrate_data_to_unified_structure.py", "streamlit_team_management_modular\\real_demo.py", "streamlit_team_management_modular\\run.py", "streamlit_team_management_modular\\search_intelligent_matching.py", "streamlit_team_management_modular\\setup_word_template.py", "streamlit_team_management_modular\\simple_template_test.py", "streamlit_team_management_modular\\simple_visual_demo.py", "streamlit_team_management_modular\\standalone_diagnosis.py", "streamlit_team_management_modular\\start.py", "streamlit_team_management_modular\\test_actual_word_generation.py", "streamlit_team_management_modular\\test_all_related_functions.py", "streamlit_team_management_modular\\test_auto_fill_fix.py", "streamlit_team_management_modular\\test_auto_workflow.py", "streamlit_team_management_modular\\test_cache_impact_analysis.py", "streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_clean_template_final.py", "streamlit_team_management_modular\\test_color_fix_verification.py", "streamlit_team_management_modular\\test_complete_fix_verification.py", "streamlit_team_management_modular\\test_complete_word_generation.py", "streamlit_team_management_modular\\test_complete_workflow_fix.py", "streamlit_team_management_modular\\test_comprehensive_fix.py", "streamlit_team_management_modular\\test_contact_fix.py", "streamlit_team_management_modular\\test_contact_fix_verification.py", "streamlit_team_management_modular\\test_contact_info_flow.py", "streamlit_team_management_modular\\test_contact_issue_analysis.py", "streamlit_team_management_modular\\test_contact_mapping_analysis.py", "streamlit_team_management_modular\\test_correct_fashion_api.py", "streamlit_team_management_modular\\test_current_auto_fill.py", "streamlit_team_management_modular\\test_current_template_usage.py", "streamlit_team_management_modular\\test_dual_api_config.py", "streamlit_team_management_modular\\test_enhanced_ai_integration.py", "streamlit_team_management_modular\\test_enhanced_chat_integration.py", "streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\test_fashion_api_diagnosis.py", "streamlit_team_management_modular\\test_final_contact_fix_verification.py", "streamlit_team_management_modular\\test_final_fix.py", "streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_hardcoded_files_impact.py", "streamlit_team_management_modular\\test_java_data_mapping.py", "streamlit_team_management_modular\\test_java_placeholder_necessity.py", "streamlit_team_management_modular\\test_loading_experience.py", "streamlit_team_management_modular\\test_logo_generation.py", "streamlit_team_management_modular\\test_missing_fields_comprehensive.py", "streamlit_team_management_modular\\test_modify_original_config.py", "streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_new_user_issues.py", "streamlit_team_management_modular\\test_openai_api_config.py", "streamlit_team_management_modular\\test_placeholder_replacement_verification.py", "streamlit_team_management_modular\\test_player_data_fix.py", "streamlit_team_management_modular\\test_real_contact_issue_debug.py", "streamlit_team_management_modular\\test_real_fashion_api.py", "streamlit_team_management_modular\\test_real_logo_generation.py", "streamlit_team_management_modular\\test_real_tianyi369_workflow.py", "streamlit_team_management_modular\\test_real_user_data_issues.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_simple_api.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_solution_implementation.py", "streamlit_team_management_modular\\test_spaced_placeholders.py", "streamlit_team_management_modular\\test_specific_issues.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py", "streamlit_team_management_modular\\test_template_content_analysis.py", "streamlit_team_management_modular\\test_template_fixes.py", "streamlit_team_management_modular\\test_template_management.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py", "streamlit_team_management_modular\\test_template_upload_fix.py", "streamlit_team_management_modular\\test_tianyi369_fix.py", "streamlit_team_management_modular\\test_unified_data_storage.py", "streamlit_team_management_modular\\test_upload.py", "streamlit_team_management_modular\\test_user_fixed_template.py", "streamlit_team_management_modular\\test_word_content_verification.py", "streamlit_team_management_modular\\test_word_generation_diagnosis.py", "streamlit_team_management_modular\\test_word_integration.py", "streamlit_team_management_modular\\verify_config_change.py", "streamlit_team_management_modular\\verify_manual_fix.py", "streamlit_team_management_modular\\verify_word_document.py", "streamlit_team_management_modular\\pages\\ai_extraction.py", "streamlit_team_management_modular\\pages\\AI_Photo_Editor.py"], "analyze_latest_word_document": ["streamlit_team_management_modular\\analyze_current_user_issues.py", "streamlit_team_management_modular\\analyze_word_content.py"], "generate_fix_recommendations": ["streamlit_team_management_modular\\analyze_current_user_issues.py", "streamlit_team_management_modular\\test_contact_mapping_analysis.py"], "analyze_template_placeholders": ["streamlit_team_management_modular\\analyze_template_placeholders.py", "streamlit_team_management_modular\\comprehensive_word_test.py"], "configure_page": ["streamlit_team_management_modular\\app.py", "streamlit_team_management_modular\\pages\\ai_extraction.py", "streamlit_team_management_modular\\pages\\AI_Photo_Editor.py"], "check_template_placeholders": ["streamlit_team_management_modular\\check_template_placeholders.py", "streamlit_team_management_modular\\test_word_content_verification.py"], "__init__": ["streamlit_team_management_modular\\cleanup_old_directories.py", "streamlit_team_management_modular\\migrate_data_to_unified_structure.py", "streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_fashion_api_diagnosis.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py", "streamlit_team_management_modular\\test_template_upload_fix.py", "streamlit_team_management_modular\\test_unified_data_storage.py", "streamlit_team_management_modular\\word_generator_service.py", "streamlit_team_management_modular\\backup_before_fix\\fashion_workflow.py", "streamlit_team_management_modular\\backup_before_fix\\fashion_workflow_service.py", "streamlit_team_management_modular\\components\\ai_chat.py", "streamlit_team_management_modular\\components\\auth_component.py", "streamlit_team_management_modular\\components\\batch_upload.py", "streamlit_team_management_modular\\components\\fashion_workflow.py", "streamlit_team_management_modular\\components\\loading_manager.py", "streamlit_team_management_modular\\components\\photo_processing.py", "streamlit_team_management_modular\\components\\player_form.py", "streamlit_team_management_modular\\components\\player_list.py", "streamlit_team_management_modular\\components\\sidebar.py", "streamlit_team_management_modular\\components\\standalone_image_editor.py", "streamlit_team_management_modular\\components\\template_selector.py", "streamlit_team_management_modular\\data\\file_manager.py", "streamlit_team_management_modular\\data\\player_repository.py", "streamlit_team_management_modular\\data\\team_repository.py", "streamlit_team_management_modular\\services\\ai_image_engine.py", "streamlit_team_management_modular\\services\\ai_image_generation_service.py", "streamlit_team_management_modular\\services\\ai_service.py", "streamlit_team_management_modular\\services\\auth_service.py", "streamlit_team_management_modular\\services\\data_bridge_service.py", "streamlit_team_management_modular\\services\\enhanced_ai_assistant.py", "streamlit_team_management_modular\\services\\enhanced_ai_service.py", "streamlit_team_management_modular\\services\\enhanced_data_manager.py", "streamlit_team_management_modular\\services\\export_service.py", "streamlit_team_management_modular\\services\\fashion_api_service.py", "streamlit_team_management_modular\\services\\fashion_api_service.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py", "streamlit_team_management_modular\\services\\field_metadata.py", "streamlit_team_management_modular\\services\\photo_service.py", "streamlit_team_management_modular\\services\\player_service.py", "streamlit_team_management_modular\\services\\team_service.py", "streamlit_team_management_modular\\services\\template_service.py", "streamlit_team_management_modular\\utils\\safe_file_manager.py", "streamlit_team_management_modular\\utils\\smart_cache_manager.py"], "log_action": ["streamlit_team_management_modular\\cleanup_old_directories.py", "streamlit_team_management_modular\\migrate_data_to_unified_structure.py"], "test_enhanced_ai_assistant": ["streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\test_current_auto_fill.py"], "is_valid_value": ["streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\debug_ai_export_data.py", "streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\test_color_fix_verification.py", "streamlit_team_management_modular\\test_complete_fix_verification.py", "streamlit_team_management_modular\\test_current_auto_fill.py", "streamlit_team_management_modular\\test_missing_fields_comprehensive.py", "streamlit_team_management_modular\\test_real_user_data_issues.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_tianyi369_fix.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "auto_fill_with_contact": ["streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\debug_ai_export_data.py", "streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\test_auto_fill_fix.py", "streamlit_team_management_modular\\test_complete_fix_verification.py", "streamlit_team_management_modular\\test_current_auto_fill.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_tianyi369_fix.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "check_word_document_content": ["streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\test_real_user_data_issues.py"], "create_fixed_template": ["streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py"], "verify_fixed_template": ["streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\fix_template_placeholders.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py"], "test_fixed_template": ["streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\fix_template_placeholders.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py"], "check_colors_in_generated_word": ["streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\fix_color_placeholders_in_template.py"], "test_workflow_processing": ["streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\test_real_user_scenario.py"], "test_word_generation": ["streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\fix_auto_fill_complete.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_word_integration.py", "streamlit_team_management_modular\\verify_config_change.py"], "check_word_content": ["streamlit_team_management_modular\\debug_data_flow.py", "streamlit_team_management_modular\\final_contact_verification.py", "streamlit_team_management_modular\\test_final_fix.py", "streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_user_fixed_template.py"], "check_generated_word_content": ["streamlit_team_management_modular\\debug_new_user_data.py", "streamlit_team_management_modular\\test_contact_fix.py"], "restore_original_config": ["streamlit_team_management_modular\\final_comprehensive_test.py", "streamlit_team_management_modular\\test_all_related_functions.py", "streamlit_team_management_modular\\test_hardcoded_files_impact.py", "streamlit_team_management_modular\\test_modify_original_config.py"], "test_complete_workflow": ["streamlit_team_management_modular\\fix_intelligent_matching_issue.py", "streamlit_team_management_modular\\test_logo_generation.py"], "check_generated_file_content": ["streamlit_team_management_modular\\fix_template_placeholders.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py"], "test_fixed_logic": ["streamlit_team_management_modular\\fix_word_generation.py", "streamlit_team_management_modular\\test_after_fix.py"], "test_fashion_api": ["streamlit_team_management_modular\\real_demo.py", "streamlit_team_management_modular\\test_correct_fashion_api.py"], "wait_for_task_completion": ["streamlit_team_management_modular\\real_demo.py", "streamlit_team_management_modular\\test_correct_fashion_api.py"], "download_result_image": ["streamlit_team_management_modular\\real_demo.py", "streamlit_team_management_modular\\test_correct_fashion_api.py"], "backup_and_modify_config": ["streamlit_team_management_modular\\test_all_related_functions.py", "streamlit_team_management_modular\\test_hardcoded_files_impact.py", "streamlit_team_management_modular\\test_modify_original_config.py"], "get": ["streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "__setitem__": ["streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "__getitem__": ["streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "__contains__": ["streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "test_with_color_data": ["streamlit_team_management_modular\\test_comprehensive_fix.py", "streamlit_team_management_modular\\test_word_content_verification.py"], "test_imports": ["streamlit_team_management_modular\\test_enhanced_ai_integration.py", "streamlit_team_management_modular\\test_imports.py"], "print_integration_summary": ["streamlit_team_management_modular\\test_enhanced_ai_integration.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py"], "test_enhanced_ai_service_functions": ["streamlit_team_management_modular\\test_enhanced_chat_integration.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py"], "extract_word_content": ["streamlit_team_management_modular\\test_final_contact_fix_verification.py", "streamlit_team_management_modular\\test_placeholder_replacement_verification.py", "streamlit_team_management_modular\\verify_word_document.py"], "error": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_real_tianyi369_workflow.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "warning": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "info": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "success": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "progress": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_final_integration.py"], "__enter__": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_final_integration.py"], "__exit__": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_final_integration.py"], "test_template_structure": ["streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_user_fixed_template.py"], "test_java_generation": ["streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_user_fixed_template.py"], "test_python_generation": ["streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_user_fixed_template.py"], "test_api_connectivity": ["streamlit_team_management_modular\\test_openai_api_config.py", "streamlit_team_management_modular\\test_real_logo_generation.py"], "setup_test_environment": ["streamlit_team_management_modular\\test_real_fashion_api.py", "streamlit_team_management_modular\\test_unified_data_storage.py"], "auto_fill_color": ["streamlit_team_management_modular\\test_tianyi369_fix.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "_save_workflow_result": ["streamlit_team_management_modular\\backup_before_fix\\fashion_workflow_service.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "__post_init__": ["streamlit_team_management_modular\\components\\loading_manager.py", "streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py", "streamlit_team_management_modular\\models\\player.py", "streamlit_team_management_modular\\models\\player.py", "streamlit_team_management_modular\\models\\team.py", "streamlit_team_management_modular\\services\\field_metadata.py"], "decorator": ["streamlit_team_management_modular\\utils\\smart_cache_manager.py", "streamlit_team_management_modular\\utils\\smart_cache_manager.py", "streamlit_team_management_modular\\utils\\smart_cache_manager.py", "streamlit_team_management_modular\\utils\\smart_cache_manager.py", "streamlit_team_management_modular\\utils\\smart_cache_manager.py"]}, "classes": {"MockSessionState": ["streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "WordGeneratorSettings": ["streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\config\\env_settings.py", "streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"], "MockStreamlit": ["streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py"], "FashionWorkflowComponent": ["streamlit_team_management_modular\\backup_before_fix\\fashion_workflow.py", "streamlit_team_management_modular\\components\\fashion_workflow.py"], "FashionWorkflowService": ["streamlit_team_management_modular\\backup_before_fix\\fashion_workflow_service.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "PathSettings": ["streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"], "AISettings": ["streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py"], "AppSettings": ["streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"]}}, "redundant_files": [{"path": "streamlit_team_management_modular\\comprehensive_debug_test.py", "pattern": ".*_test\\.py$", "size": 15169, "modified": "2025-08-31 16:12:56.515620"}, {"path": "streamlit_team_management_modular\\comprehensive_template_test.py", "pattern": ".*_test\\.py$", "size": 16074, "modified": "2025-08-31 18:17:44.234344"}, {"path": "streamlit_team_management_modular\\comprehensive_word_test.py", "pattern": ".*_test\\.py$", "size": 16397, "modified": "2025-08-31 18:17:44.237343"}, {"path": "streamlit_team_management_modular\\final_comprehensive_test.py", "pattern": ".*_test\\.py$", "size": 9213, "modified": "2025-08-31 18:17:44.245841"}, {"path": "streamlit_team_management_modular\\final_debug_test.py", "pattern": ".*_test\\.py$", "size": 9343, "modified": "2025-08-31 14:33:55.820055"}, {"path": "streamlit_team_management_modular\\simple_template_test.py", "pattern": ".*_test\\.py$", "size": 1109, "modified": "2025-08-24 15:18:24.686244"}, {"path": "streamlit_team_management_modular\\test_15players_config.py", "pattern": "test_.*\\.py$", "size": 3660, "modified": "2025-08-30 13:37:00.589237"}, {"path": "streamlit_team_management_modular\\test_actual_word_generation.py", "pattern": "test_.*\\.py$", "size": 10719, "modified": "2025-08-31 14:09:16.962861"}, {"path": "streamlit_team_management_modular\\test_after_fix.py", "pattern": "test_.*\\.py$", "size": 1523, "modified": "2025-08-24 14:35:00.273310"}, {"path": "streamlit_team_management_modular\\test_ai_improvements.py", "pattern": "test_.*\\.py$", "size": 2942, "modified": "2025-08-20 14:20:47.648350"}, {"path": "streamlit_team_management_modular\\test_all_related_functions.py", "pattern": "test_.*\\.py$", "size": 10002, "modified": "2025-08-31 18:17:44.249343"}, {"path": "streamlit_team_management_modular\\test_app.py", "pattern": "test_.*\\.py$", "size": 805, "modified": "2025-08-23 22:53:12.214615"}, {"path": "streamlit_team_management_modular\\test_auto_ai_sensing.py", "pattern": "test_.*\\.py$", "size": 3898, "modified": "2025-08-20 14:31:01.270845"}, {"path": "streamlit_team_management_modular\\test_auto_fill_fix.py", "pattern": "test_.*\\.py$", "size": 11516, "modified": "2025-08-30 19:03:20.262538"}, {"path": "streamlit_team_management_modular\\test_auto_workflow.py", "pattern": "test_.*\\.py$", "size": 8905, "modified": "2025-08-22 11:56:24.312238"}, {"path": "streamlit_team_management_modular\\test_cache_impact_analysis.py", "pattern": "test_.*\\.py$", "size": 9307, "modified": "2025-08-20 17:55:23.858837"}, {"path": "streamlit_team_management_modular\\test_cache_impact_fixed.py", "pattern": "test_.*\\.py$", "size": 12359, "modified": "2025-08-20 17:58:26.467093"}, {"path": "streamlit_team_management_modular\\test_cache_impact_regression.py", "pattern": "test_.*\\.py$", "size": 18328, "modified": "2025-08-20 17:53:44.654695"}, {"path": "streamlit_team_management_modular\\test_clean_template_final.py", "pattern": "test_.*\\.py$", "size": 13276, "modified": "2025-08-31 18:17:44.254345"}, {"path": "streamlit_team_management_modular\\test_color_fix_verification.py", "pattern": "test_.*\\.py$", "size": 9229, "modified": "2025-08-31 14:22:34.564906"}, {"path": "streamlit_team_management_modular\\test_complete_fix_verification.py", "pattern": "test_.*\\.py$", "size": 11098, "modified": "2025-08-31 14:24:37.364728"}, {"path": "streamlit_team_management_modular\\test_complete_word_generation.py", "pattern": "test_.*\\.py$", "size": 13256, "modified": "2025-08-21 16:31:24.826030"}, {"path": "streamlit_team_management_modular\\test_complete_workflow_fix.py", "pattern": "test_.*\\.py$", "size": 10827, "modified": "2025-08-31 18:55:02.744112"}, {"path": "streamlit_team_management_modular\\test_comprehensive_fix.py", "pattern": "test_.*\\.py$", "size": 15774, "modified": "2025-08-30 18:08:38.018316"}, {"path": "streamlit_team_management_modular\\test_contact_fix.py", "pattern": "test_.*\\.py$", "size": 10085, "modified": "2025-08-30 16:35:55.511399"}, {"path": "streamlit_team_management_modular\\test_contact_fix_verification.py", "pattern": "test_.*\\.py$", "size": 13317, "modified": "2025-08-31 18:17:44.256840"}, {"path": "streamlit_team_management_modular\\test_contact_info_flow.py", "pattern": "test_.*\\.py$", "size": 11484, "modified": "2025-08-30 14:02:18.066910"}, {"path": "streamlit_team_management_modular\\test_contact_issue_analysis.py", "pattern": "test_.*\\.py$", "size": 13802, "modified": "2025-08-31 18:17:44.263839"}, {"path": "streamlit_team_management_modular\\test_contact_mapping_analysis.py", "pattern": "test_.*\\.py$", "size": 10679, "modified": "2025-08-31 18:17:44.266839"}, {"path": "streamlit_team_management_modular\\test_correct_fashion_api.py", "pattern": "test_.*\\.py$", "size": 12378, "modified": "2025-08-21 12:39:35.969601"}, {"path": "streamlit_team_management_modular\\test_current_auto_fill.py", "pattern": "test_.*\\.py$", "size": 9149, "modified": "2025-08-31 15:05:54.727439"}, {"path": "streamlit_team_management_modular\\test_current_template_usage.py", "pattern": "test_.*\\.py$", "size": 16618, "modified": "2025-08-31 18:17:44.269839"}, {"path": "streamlit_team_management_modular\\test_dual_api_config.py", "pattern": "test_.*\\.py$", "size": 7370, "modified": "2025-08-22 13:35:34.265421"}, {"path": "streamlit_team_management_modular\\test_enhanced_ai_integration.py", "pattern": "test_.*\\.py$", "size": 8364, "modified": "2025-08-20 16:34:48.245926"}, {"path": "streamlit_team_management_modular\\test_enhanced_chat_integration.py", "pattern": "test_.*\\.py$", "size": 8687, "modified": "2025-08-20 16:49:44.468225"}, {"path": "streamlit_team_management_modular\\test_env_config.py", "pattern": "test_.*\\.py$", "size": 4989, "modified": "2025-08-31 18:17:44.219341"}, {"path": "streamlit_team_management_modular\\test_fashion_api_diagnosis.py", "pattern": "test_.*\\.py$", "size": 10174, "modified": "2025-08-29 13:36:22.084594"}, {"path": "streamlit_team_management_modular\\test_final_contact_fix_verification.py", "pattern": "test_.*\\.py$", "size": 13394, "modified": "2025-08-31 18:17:44.272341"}, {"path": "streamlit_team_management_modular\\test_final_fix.py", "pattern": "test_.*\\.py$", "size": 6647, "modified": "2025-08-30 17:26:03.463502"}, {"path": "streamlit_team_management_modular\\test_final_integration.py", "pattern": "test_.*\\.py$", "size": 9120, "modified": "2025-08-29 23:04:57.790226"}, {"path": "streamlit_team_management_modular\\test_fixed_fashion_v2.py", "pattern": "test_.*\\.py$", "size": 2792, "modified": "2025-08-23 23:21:08.305939"}, {"path": "streamlit_team_management_modular\\test_full_integration_compatibility.py", "pattern": "test_.*\\.py$", "size": 14824, "modified": "2025-08-20 17:11:53.985169"}, {"path": "streamlit_team_management_modular\\test_hardcoded_files_impact.py", "pattern": "test_.*\\.py$", "size": 8804, "modified": "2025-08-31 18:17:44.275339"}, {"path": "streamlit_team_management_modular\\test_imports.py", "pattern": "test_.*\\.py$", "size": 4151, "modified": "2025-08-20 01:28:10.269994"}, {"path": "streamlit_team_management_modular\\test_java_data_mapping.py", "pattern": "test_.*\\.py$", "size": 8080, "modified": "2025-08-31 18:17:44.278841"}, {"path": "streamlit_team_management_modular\\test_java_placeholder_necessity.py", "pattern": "test_.*\\.py$", "size": 17318, "modified": "2025-08-31 18:17:44.283840"}, {"path": "streamlit_team_management_modular\\test_loading_experience.py", "pattern": "test_.*\\.py$", "size": 12094, "modified": "2025-08-20 17:29:23.697080"}, {"path": "streamlit_team_management_modular\\test_logo_generation.py", "pattern": "test_.*\\.py$", "size": 10453, "modified": "2025-08-22 13:03:46.504561"}, {"path": "streamlit_team_management_modular\\test_missing_fields_comprehensive.py", "pattern": "test_.*\\.py$", "size": 9410, "modified": "2025-08-31 14:07:55.991776"}, {"path": "streamlit_team_management_modular\\test_modify_original_config.py", "pattern": "test_.*\\.py$", "size": 5142, "modified": "2025-08-31 18:17:44.225342"}, {"path": "streamlit_team_management_modular\\test_new_template.py", "pattern": "test_.*\\.py$", "size": 18903, "modified": "2025-08-31 18:17:44.292840"}, {"path": "streamlit_team_management_modular\\test_new_user_issues.py", "pattern": "test_.*\\.py$", "size": 15941, "modified": "2025-08-30 18:43:26.313740"}, {"path": "streamlit_team_management_modular\\test_new_workflow.py", "pattern": "test_.*\\.py$", "size": 3409, "modified": "2025-08-23 23:28:48.818621"}, {"path": "streamlit_team_management_modular\\test_openai_api_config.py", "pattern": "test_.*\\.py$", "size": 6493, "modified": "2025-08-22 13:28:05.979226"}, {"path": "streamlit_team_management_modular\\test_personalized_ai_messages.py", "pattern": "test_.*\\.py$", "size": 4817, "modified": "2025-08-20 14:38:12.441634"}, {"path": "streamlit_team_management_modular\\test_placeholder_replacement_verification.py", "pattern": "test_.*\\.py$", "size": 16851, "modified": "2025-08-31 18:17:44.297341"}, {"path": "streamlit_team_management_modular\\test_player_data_fix.py", "pattern": "test_.*\\.py$", "size": 6857, "modified": "2025-08-21 16:27:16.099916"}, {"path": "streamlit_team_management_modular\\test_real_contact_issue_debug.py", "pattern": ".*_debug\\.py$", "size": 15862, "modified": "2025-08-31 18:17:44.302340"}, {"path": "streamlit_team_management_modular\\test_real_fashion_api.py", "pattern": "test_.*\\.py$", "size": 8305, "modified": "2025-08-21 12:25:21.057164"}, {"path": "streamlit_team_management_modular\\test_real_logo_generation.py", "pattern": "test_.*\\.py$", "size": 10918, "modified": "2025-08-22 13:17:13.240014"}, {"path": "streamlit_team_management_modular\\test_real_tianyi369_workflow.py", "pattern": "test_.*\\.py$", "size": 9045, "modified": "2025-08-31 18:45:47.534482"}, {"path": "streamlit_team_management_modular\\test_real_user_data_issues.py", "pattern": "test_.*\\.py$", "size": 12717, "modified": "2025-08-31 14:10:50.498247"}, {"path": "streamlit_team_management_modular\\test_real_user_scenario.py", "pattern": "test_.*\\.py$", "size": 11421, "modified": "2025-08-31 15:08:56.014991"}, {"path": "streamlit_team_management_modular\\test_simple_api.py", "pattern": "test_.*\\.py$", "size": 8216, "modified": "2025-08-21 12:29:33.513458"}, {"path": "streamlit_team_management_modular\\test_smart_cache_performance.py", "pattern": "test_.*\\.py$", "size": 11630, "modified": "2025-08-20 17:47:59.863180"}, {"path": "streamlit_team_management_modular\\test_solution_implementation.py", "pattern": "test_.*\\.py$", "size": 10134, "modified": "2025-08-24 14:33:28.296809"}, {"path": "streamlit_team_management_modular\\test_spaced_placeholders.py", "pattern": "test_.*\\.py$", "size": 20187, "modified": "2025-08-31 18:17:44.309341"}, {"path": "streamlit_team_management_modular\\test_specific_issues.py", "pattern": "test_.*\\.py$", "size": 13531, "modified": "2025-08-30 17:55:43.111917"}, {"path": "streamlit_team_management_modular\\test_streamlit_word_integration.py", "pattern": "test_.*\\.py$", "size": 7018, "modified": "2025-08-29 23:06:57.189989"}, {"path": "streamlit_team_management_modular\\test_template_content_analysis.py", "pattern": "test_.*\\.py$", "size": 11306, "modified": "2025-08-31 18:17:44.320341"}, {"path": "streamlit_team_management_modular\\test_template_fixes.py", "pattern": "test_.*\\.py$", "size": 9629, "modified": "2025-08-22 12:30:38.527803"}, {"path": "streamlit_team_management_modular\\test_template_management.py", "pattern": "test_.*\\.py$", "size": 9150, "modified": "2025-08-22 12:18:42.244063"}, {"path": "streamlit_team_management_modular\\test_template_placeholder_fix.py", "pattern": "test_.*\\.py$", "size": 16906, "modified": "2025-08-31 18:17:44.323346"}, {"path": "streamlit_team_management_modular\\test_template_upload_fix.py", "pattern": "test_.*\\.py$", "size": 5221, "modified": "2025-08-24 15:16:55.058240"}, {"path": "streamlit_team_management_modular\\test_tianyi369_fix.py", "pattern": "test_.*\\.py$", "size": 10276, "modified": "2025-08-31 18:44:19.173518"}, {"path": "streamlit_team_management_modular\\test_unified_data_storage.py", "pattern": "test_.*\\.py$", "size": 14972, "modified": "2025-08-23 21:43:50.085738"}, {"path": "streamlit_team_management_modular\\test_upload.py", "pattern": "test_.*\\.py$", "size": 3810, "modified": "2025-08-20 03:37:32.396898"}, {"path": "streamlit_team_management_modular\\test_user_fixed_template.py", "pattern": "test_.*\\.py$", "size": 14859, "modified": "2025-08-31 18:17:44.331842"}, {"path": "streamlit_team_management_modular\\test_word_content_verification.py", "pattern": "test_.*\\.py$", "size": 10476, "modified": "2025-08-31 18:17:44.335842"}, {"path": "streamlit_team_management_modular\\test_word_generation_diagnosis.py", "pattern": "test_.*\\.py$", "size": 8242, "modified": "2025-08-24 14:30:52.127392"}, {"path": "streamlit_team_management_modular\\test_word_integration.py", "pattern": "test_.*\\.py$", "size": 5349, "modified": "2025-08-21 16:04:20.771757"}, {"path": "streamlit_team_management_modular\\config\\settings_original_backup.py", "pattern": ".*_backup\\.py$", "size": 7892, "modified": "2025-08-24 15:02:08.353944"}], "config_conflicts": {"template_conflicts": {"WORD_TEMPLATE_PATH": ["streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\config\\env_settings.py"], "] = template_path\n    print(f": ["streamlit_team_management_modular\\test_env_config.py"], ")\n    \n    try:\n        # 导入环境变量配置\n        from config.env_settings import env_word_settings\n        from word_generator_service import WordGeneratorService\n        \n        # 获取配置路径\n        paths = env_word_settings.get_absolute_paths(": ["streamlit_team_management_modular\\test_env_config.py"], " in os.environ:\n            del os.environ[": ["streamlit_team_management_modular\\test_env_config.py"], "]\n\ndef main():\n    ": ["streamlit_team_management_modular\\test_env_config.py"], ")\n        else:\n            print(": ["streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\test_modify_original_config.py"], "../word_zc/ai-football-generator/template.docx": ["streamlit_team_management_modular\\test_modify_original_config.py", "streamlit_team_management_modular\\config\\settings_original_backup.py"], "../word_zc/template_15players_fixed.docx": ["streamlit_team_management_modular\\test_modify_original_config.py", "streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"]}, "jar_conflicts": {"../word_zc/ai-football-generator/target/word-generator.jar": ["streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\config\\env_settings.py", "streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"]}}, "llm_confusion_sources": [{"type": "similar_naming", "base_name": "README", "files": ["streamlit_team_management_modular\\README.md", "streamlit_team_management_modular\\poi-tl\\README.md", "streamlit_team_management_modular\\poi-tl\\poi-tl-plugin-markdown\\src\\test\\resources\\markdown\\README.md", "word_zc\\ai-football-generator\\README.md"], "risk_level": "high"}, {"type": "similar_naming", "base_name": "__init__", "files": ["streamlit_team_management_modular\\__init__.py", "streamlit_team_management_modular\\components\\__init__.py", "streamlit_team_management_modular\\config\\__init__.py", "streamlit_team_management_modular\\data\\__init__.py", "streamlit_team_management_modular\\models\\__init__.py", "streamlit_team_management_modular\\services\\__init__.py", "streamlit_team_management_modular\\utils\\__init__.py"], "risk_level": "high"}, {"type": "similar_naming", "base_name": "fashion_workflow", "files": ["streamlit_team_management_modular\\backup_before_fix\\fashion_workflow.py", "streamlit_team_management_modular\\components\\fashion_workflow.py"], "risk_level": "medium"}, {"type": "similar_naming", "base_name": "fashion_workflow_service", "files": ["streamlit_team_management_modular\\backup_before_fix\\fashion_workflow_service.py", "streamlit_team_management_modular\\services\\fashion_workflow_service.py"], "risk_level": "medium"}, {"type": "similar_naming", "base_name": "petstore", "files": ["streamlit_team_management_modular\\poi-tl\\poi-tl\\src\\test\\resources\\petstore.json", "streamlit_team_management_modular\\poi-tl\\poi-tl-plugin-highlight\\src\\test\\resources\\swagger\\petstore.json"], "risk_level": "medium"}, {"type": "similar_naming", "base_name": "basic", "files": ["streamlit_team_management_modular\\poi-tl\\poi-tl-cli\\src\\test\\resources\\basic.md", "streamlit_team_management_modular\\poi-tl\\poi-tl-plugin-markdown\\src\\test\\resources\\markdown\\basic.md"], "risk_level": "medium"}, {"type": "multiple_settings", "files": ["streamlit_team_management_modular\\config\\env_settings.py", "streamlit_team_management_modular\\config\\settings.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py"], "risk_level": "high", "description": "多个settings文件可能导致配置混乱"}, {"type": "multiple_configs", "files": ["streamlit_team_management_modular\\fix_poi_tl_config.py", "streamlit_team_management_modular\\test_15players_config.py", "streamlit_team_management_modular\\test_dual_api_config.py", "streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\test_modify_original_config.py", "streamlit_team_management_modular\\test_openai_api_config.py", "streamlit_team_management_modular\\verify_config_change.py", "streamlit_team_management_modular\\models\\processing_config.py"], "risk_level": "high", "description": "多个config文件可能导致配置混乱"}, {"type": "test_production_mix", "files": ["streamlit_team_management_modular\\analyze_template_placeholders.py", "streamlit_team_management_modular\\check_all_templates.py", "streamlit_team_management_modular\\check_latest_word.py", "streamlit_team_management_modular\\check_template_placeholders.py", "streamlit_team_management_modular\\comprehensive_debug_test.py", "streamlit_team_management_modular\\comprehensive_template_test.py", "streamlit_team_management_modular\\comprehensive_word_test.py", "streamlit_team_management_modular\\create_clean_template.py", "streamlit_team_management_modular\\create_default_template.py", "streamlit_team_management_modular\\create_fixed_template.py", "streamlit_team_management_modular\\debug_actual_workflow.py", "streamlit_team_management_modular\\debug_ai_export_data.py", "streamlit_team_management_modular\\debug_auto_fill_failure.py", "streamlit_team_management_modular\\debug_auto_fill_issue.py", "streamlit_team_management_modular\\debug_color_placeholders.py", "streamlit_team_management_modular\\debug_data_flow.py", "streamlit_team_management_modular\\debug_fashion_task_failure.py", "streamlit_team_management_modular\\debug_java_color_processing.py", "streamlit_team_management_modular\\debug_new_user_data.py", "streamlit_team_management_modular\\debug_team_service_data.py", "streamlit_team_management_modular\\debug_tianyi369_issue.py", "streamlit_team_management_modular\\debug_tianyi909_word_generation.py", "streamlit_team_management_modular\\debug_upload.py", "streamlit_team_management_modular\\final_comprehensive_test.py", "streamlit_team_management_modular\\final_debug_test.py", "streamlit_team_management_modular\\final_template_verification.py", "streamlit_team_management_modular\\find_all_old_template_references.py", "streamlit_team_management_modular\\fix_color_placeholders_in_template.py", "streamlit_team_management_modular\\fix_template_placeholders.py", "streamlit_team_management_modular\\setup_word_template.py", "streamlit_team_management_modular\\simple_template_test.py", "streamlit_team_management_modular\\TEMPLATE_INSTALLATION_GUIDE.md", "streamlit_team_management_modular\\TEMPLATE_MANAGEMENT.md", "streamlit_team_management_modular\\test_15players_config.py", "streamlit_team_management_modular\\test_actual_word_generation.py", "streamlit_team_management_modular\\test_after_fix.py", "streamlit_team_management_modular\\test_ai_improvements.py", "streamlit_team_management_modular\\test_all_related_functions.py", "streamlit_team_management_modular\\test_app.py", "streamlit_team_management_modular\\test_auto_ai_sensing.py", "streamlit_team_management_modular\\test_auto_fill_fix.py", "streamlit_team_management_modular\\test_auto_workflow.py", "streamlit_team_management_modular\\test_cache_impact_analysis.py", "streamlit_team_management_modular\\test_cache_impact_fixed.py", "streamlit_team_management_modular\\test_cache_impact_regression.py", "streamlit_team_management_modular\\test_clean_template_final.py", "streamlit_team_management_modular\\test_color_fix_verification.py", "streamlit_team_management_modular\\test_complete_fix_verification.py", "streamlit_team_management_modular\\test_complete_word_generation.py", "streamlit_team_management_modular\\test_complete_workflow_fix.py", "streamlit_team_management_modular\\test_comprehensive_fix.py", "streamlit_team_management_modular\\test_contact_fix.py", "streamlit_team_management_modular\\test_contact_fix_verification.py", "streamlit_team_management_modular\\test_contact_info_flow.py", "streamlit_team_management_modular\\test_contact_issue_analysis.py", "streamlit_team_management_modular\\test_contact_mapping_analysis.py", "streamlit_team_management_modular\\test_correct_fashion_api.py", "streamlit_team_management_modular\\test_current_auto_fill.py", "streamlit_team_management_modular\\test_current_template_usage.py", "streamlit_team_management_modular\\test_dual_api_config.py", "streamlit_team_management_modular\\test_enhanced_ai_integration.py", "streamlit_team_management_modular\\test_enhanced_chat_integration.py", "streamlit_team_management_modular\\test_env_config.py", "streamlit_team_management_modular\\test_fashion_api_diagnosis.py", "streamlit_team_management_modular\\test_final_contact_fix_verification.py", "streamlit_team_management_modular\\test_final_fix.py", "streamlit_team_management_modular\\test_final_integration.py", "streamlit_team_management_modular\\test_fixed_fashion_v2.py", "streamlit_team_management_modular\\test_full_integration_compatibility.py", "streamlit_team_management_modular\\test_hardcoded_files_impact.py", "streamlit_team_management_modular\\test_imports.py", "streamlit_team_management_modular\\test_java_data_mapping.py", "streamlit_team_management_modular\\test_java_placeholder_necessity.py", "streamlit_team_management_modular\\test_loading_experience.py", "streamlit_team_management_modular\\test_logo_generation.py", "streamlit_team_management_modular\\test_missing_fields_comprehensive.py", "streamlit_team_management_modular\\test_modify_original_config.py", "streamlit_team_management_modular\\test_new_template.py", "streamlit_team_management_modular\\test_new_user_issues.py", "streamlit_team_management_modular\\test_new_workflow.py", "streamlit_team_management_modular\\test_openai_api_config.py", "streamlit_team_management_modular\\test_personalized_ai_messages.py", "streamlit_team_management_modular\\test_placeholder_replacement_verification.py", "streamlit_team_management_modular\\test_player_data_fix.py", "streamlit_team_management_modular\\test_real_contact_issue_debug.py", "streamlit_team_management_modular\\test_real_fashion_api.py", "streamlit_team_management_modular\\test_real_logo_generation.py", "streamlit_team_management_modular\\test_real_tianyi369_workflow.py", "streamlit_team_management_modular\\test_real_user_data_issues.py", "streamlit_team_management_modular\\test_real_user_scenario.py", "streamlit_team_management_modular\\test_simple_api.py", "streamlit_team_management_modular\\test_smart_cache_performance.py", "streamlit_team_management_modular\\test_solution_implementation.py", "streamlit_team_management_modular\\test_spaced_placeholders.py", "streamlit_team_management_modular\\test_specific_issues.py", "streamlit_team_management_modular\\test_streamlit_word_integration.py", "streamlit_team_management_modular\\test_template_content_analysis.py", "streamlit_team_management_modular\\test_template_fixes.py", "streamlit_team_management_modular\\test_template_management.py", "streamlit_team_management_modular\\test_template_placeholder_fix.py", "streamlit_team_management_modular\\test_template_upload_fix.py", "streamlit_team_management_modular\\test_tianyi369_fix.py", "streamlit_team_management_modular\\test_unified_data_storage.py", "streamlit_team_management_modular\\test_upload.py", "streamlit_team_management_modular\\test_user_fixed_template.py", "streamlit_team_management_modular\\test_word_content_verification.py", "streamlit_team_management_modular\\test_word_generation_diagnosis.py", "streamlit_team_management_modular\\test_word_integration.py", "streamlit_team_management_modular\\components\\template_selector.py", "streamlit_team_management_modular\\config\\settings_original_backup.py", "streamlit_team_management_modular\\config\\settings_test_15players.py", "streamlit_team_management_modular\\data\\backup_info.json", "streamlit_team_management_modular\\data\\cleanup_backup_record.json", "streamlit_team_management_modular\\data\\unified_storage_test_report.json", "streamlit_team_management_modular\\data\\test_user\\enhanced_ai_data\\天依369_test_ai_data.json", "streamlit_team_management_modular\\services\\template_service.py", "streamlit_team_management_modular\\utils\\debug_utils.py", "word_zc\\ai-football-generator\\debug_test.json", "word_zc\\ai-football-generator\\test_15players_template.json", "word_zc\\ai-football-generator\\test_complete_data.json", "word_zc\\ai-football-generator\\test_data.json", "word_zc\\ai-football-generator\\test_realistic_template.json", "word_zc\\ai-football-generator\\test_rename_method.json", "word_zc\\ai-football-generator\\test_simple_template.json", "word_zc\\ai-football-generator\\test_simple_test_template.json"], "risk_level": "medium", "description": "测试文件与生产文件混合可能导致LLM选择错误的代码"}], "statistics": {"total_duplicate_functions": 53, "total_duplicate_classes": 8, "total_redundant_files": 82, "total_confusion_sources": 9, "config_conflicts": 2}}