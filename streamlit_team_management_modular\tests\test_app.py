#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit应用
"""

import streamlit as st
import sys
import os

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

st.title("🧪 测试应用")

try:
    st.write("正在测试导入...")
    
    # 测试导入
    from components.sidebar import SidebarComponent
    st.success("✅ SidebarComponent导入成功！")
    
    # 创建实例
    sidebar = SidebarComponent()
    st.success("✅ SidebarComponent实例创建成功！")
    
    st.write("🎉 所有测试通过！主应用应该可以正常运行了。")
    
except Exception as e:
    st.error(f"❌ 错误: {e}")
    import traceback
    st.code(traceback.format_exc())
