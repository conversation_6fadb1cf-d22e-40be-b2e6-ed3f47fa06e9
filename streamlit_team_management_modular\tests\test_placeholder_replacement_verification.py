#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Word文档中占位符是否被正确替换
"""

import sys
import os
import json
import subprocess
import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path

def extract_word_content(docx_path):
    """提取Word文档的文本内容"""
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            # 读取document.xml文件
            with zip_file.open('word/document.xml') as xml_file:
                tree = ET.parse(xml_file)
                root = tree.getroot()
                
                # 提取所有文本内容
                text_content = []
                for elem in root.iter():
                    if elem.text:
                        text_content.append(elem.text)
                
                return ' '.join(text_content)
    except Exception as e:
        print(f"❌ 提取Word内容失败: {e}")
        return None

def test_placeholder_replacement_with_contact():
    """测试包含联系人信息时的占位符替换"""
    print("🧪 测试1: 包含联系人信息的占位符替换")
    print("=" * 60)
    
    # 创建包含联系人信息的测试数据
    test_data = {
        "teamInfo": {
            "title": "联系人占位符测试报名表",
            "organizationName": "联系人占位符测试队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五",
            "contactPerson": "赵六",
            "contactPhone": "13800138000"
        },
        "players": [
            {
                "number": "10",
                "name": "测试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入测试文件
        test_file = "test_with_contact.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件")
        print(f"📄 联系人: {test_data['teamInfo']['contactPerson']}")
        print(f"📄 联系电话: {test_data['teamInfo']['contactPhone']}")
        
        # 运行Java程序
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 查找生成的文件
            output_dir = "../word_zc/ai-football-generator/output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 提取Word内容
                    content = extract_word_content(latest_file)
                    if content:
                        print("\n📝 检查占位符替换:")
                        
                        # 检查联系人信息是否在文档中
                        has_contact_person = "赵六" in content
                        has_contact_phone = "13800138000" in content
                        has_placeholder_person = "{{contactPerson}}" in content
                        has_placeholder_phone = "{{contactPhone}}" in content
                        
                        print(f"   联系人'赵六'在文档中: {'✅ 是' if has_contact_person else '❌ 否'}")
                        print(f"   电话'13800138000'在文档中: {'✅ 是' if has_contact_phone else '❌ 否'}")
                        print(f"   占位符{{{{contactPerson}}}}仍存在: {'⚠️ 是' if has_placeholder_person else '✅ 否'}")
                        print(f"   占位符{{{{contactPhone}}}}仍存在: {'⚠️ 是' if has_placeholder_phone else '✅ 否'}")
                        
                        # 显示部分内容用于调试
                        if len(content) > 200:
                            print(f"\n📄 文档内容片段:")
                            print(f"   {content[:200]}...")
                        
                        return {
                            'success': True,
                            'has_contact_person': has_contact_person,
                            'has_contact_phone': has_contact_phone,
                            'has_placeholder_person': has_placeholder_person,
                            'has_placeholder_phone': has_placeholder_phone,
                            'content_length': len(content)
                        }
                    else:
                        print("❌ 无法提取Word内容")
                        return {'success': False, 'error': '无法提取Word内容'}
        else:
            print(f"❌ Java程序运行失败: {result.returncode}")
            return {'success': False, 'error': f'Java程序失败: {result.returncode}'}
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {'success': False, 'error': str(e)}
    finally:
        if os.path.exists("test_with_contact.json"):
            os.remove("test_with_contact.json")

def test_placeholder_replacement_without_contact():
    """测试不包含联系人信息时的占位符行为"""
    print("\n🧪 测试2: 不包含联系人信息的占位符行为")
    print("=" * 60)
    
    # 创建不包含联系人信息的测试数据
    test_data = {
        "teamInfo": {
            "title": "无联系人占位符测试报名表",
            "organizationName": "无联系人占位符测试队",
            "teamLeader": "张三",
            "coach": "李四",
            "teamDoctor": "王五"
            # 故意不包含contactPerson和contactPhone
        },
        "players": [
            {
                "number": "10",
                "name": "测试球员",
                "photoPath": "java_word_photos/player1.png"
            }
        ],
        "config": {
            "templatePath": "../word_zc/template_15players_fixed.docx",
            "outputDir": "output",
            "photosDir": "java_word_photos"
        }
    }
    
    try:
        # 写入测试文件
        test_file = "test_without_contact.json"
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件（不包含联系人字段）")
        
        # 运行Java程序
        result = subprocess.run(
            ["java", "-cp", "../word_zc/ai-football-generator/target/word-generator.jar", 
             "CommandLineMain", test_file],
            capture_output=True,
            text=True,
            encoding='utf-8',
            timeout=30,
            errors='ignore'
        )
        
        if result.returncode == 0:
            print("✅ Java程序运行成功")
            
            # 查找生成的文件
            output_dir = "../word_zc/ai-football-generator/output"
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
                if files:
                    latest_file = max([os.path.join(output_dir, f) for f in files], 
                                    key=os.path.getmtime)
                    
                    print(f"📄 生成文件: {latest_file}")
                    
                    # 提取Word内容
                    content = extract_word_content(latest_file)
                    if content:
                        print("\n📝 检查占位符行为:")
                        
                        # 检查占位符是否仍然存在
                        has_placeholder_person = "{{contactPerson}}" in content
                        has_placeholder_phone = "{{contactPhone}}" in content
                        
                        print(f"   占位符{{{{contactPerson}}}}仍存在: {'⚠️ 是' if has_placeholder_person else '✅ 否'}")
                        print(f"   占位符{{{{contactPhone}}}}仍存在: {'⚠️ 是' if has_placeholder_phone else '✅ 否'}")
                        
                        if has_placeholder_person or has_placeholder_phone:
                            print("💡 结论: 缺少联系人字段时，占位符未被替换")
                        else:
                            print("💡 结论: 即使缺少联系人字段，占位符也被处理了（可能替换为空值）")
                        
                        return {
                            'success': True,
                            'has_placeholder_person': has_placeholder_person,
                            'has_placeholder_phone': has_placeholder_phone,
                            'content_length': len(content)
                        }
                    else:
                        print("❌ 无法提取Word内容")
                        return {'success': False, 'error': '无法提取Word内容'}
        else:
            print(f"❌ Java程序运行失败: {result.returncode}")
            return {'success': False, 'error': f'Java程序失败: {result.returncode}'}
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {'success': False, 'error': str(e)}
    finally:
        if os.path.exists("test_without_contact.json"):
            os.remove("test_without_contact.json")

def test_python_word_generation_content():
    """测试Python方式生成的Word内容"""
    print("\n🧪 测试3: Python方式生成的Word内容")
    print("=" * 60)
    
    try:
        from word_generator_service import WordGeneratorService
        from config.settings import app_settings
        
        # 准备测试数据
        team_data = {
            'name': 'Python内容测试队',
            'leader': '张三',
            'coach': '李四',
            'doctor': '王五',
            'contact_person': '赵六',
            'contact_phone': '13800138000'
        }
        
        players_data = [
            {
                'name': '测试球员',
                'jersey_number': '10',
                'photo': 'word_zc/ai-football-generator/java_word_photos/player1.png'
            }
        ]
        
        # 获取配置路径
        paths = app_settings.word_generator.get_absolute_paths("test_user", app_settings.paths)
        
        # 创建Word生成服务
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        # 生成Word文档
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            print(f"✅ Python方式生成成功: {result['file_path']}")
            
            # 提取Word内容
            content = extract_word_content(result['file_path'])
            if content:
                print("\n📝 检查Python生成的内容:")
                
                # 检查联系人信息是否在文档中
                has_contact_person = "赵六" in content
                has_contact_phone = "13800138000" in content
                has_placeholder_person = "{{contactPerson}}" in content
                has_placeholder_phone = "{{contactPhone}}" in content
                
                print(f"   联系人'赵六'在文档中: {'✅ 是' if has_contact_person else '❌ 否'}")
                print(f"   电话'13800138000'在文档中: {'✅ 是' if has_contact_phone else '❌ 否'}")
                print(f"   占位符{{{{contactPerson}}}}仍存在: {'⚠️ 是' if has_placeholder_person else '✅ 否'}")
                print(f"   占位符{{{{contactPhone}}}}仍存在: {'⚠️ 是' if has_placeholder_phone else '✅ 否'}")
                
                return {
                    'success': True,
                    'has_contact_person': has_contact_person,
                    'has_contact_phone': has_contact_phone,
                    'has_placeholder_person': has_placeholder_person,
                    'has_placeholder_phone': has_placeholder_phone,
                    'content_length': len(content)
                }
            else:
                print("❌ 无法提取Word内容")
                return {'success': False, 'error': '无法提取Word内容'}
        else:
            print(f"❌ Python方式生成失败: {result['message']}")
            return {'success': False, 'error': result['message']}
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return {'success': False, 'error': str(e)}

def main():
    """主测试函数"""
    print("🎯 Word文档占位符替换验证测试")
    print("=" * 70)
    
    # 运行所有测试
    results = {}
    
    # 测试1: 包含联系人信息的占位符替换
    results['with_contact'] = test_placeholder_replacement_with_contact()
    
    # 测试2: 不包含联系人信息的占位符行为
    results['without_contact'] = test_placeholder_replacement_without_contact()
    
    # 测试3: Python方式生成的Word内容
    results['python_content'] = test_python_word_generation_content()
    
    # 分析结果
    print("\n📊 占位符替换分析")
    print("=" * 70)
    
    print("🎯 Java修复的实际影响:")
    
    # 分析Java方式的结果
    java_with_contact = results.get('with_contact', {})
    java_without_contact = results.get('without_contact', {})
    python_result = results.get('python_content', {})
    
    if java_with_contact.get('success'):
        if java_with_contact.get('has_contact_person') and java_with_contact.get('has_contact_phone'):
            print("✅ Java修复有效: 联系人信息正确出现在文档中")
        else:
            print("⚠️ Java修复可能无效: 联系人信息未出现在文档中")
        
        if java_with_contact.get('has_placeholder_person') or java_with_contact.get('has_placeholder_phone'):
            print("⚠️ 占位符未完全替换: 仍有占位符残留")
        else:
            print("✅ 占位符完全替换: 无占位符残留")
    
    if python_result.get('success'):
        if python_result.get('has_contact_person') and python_result.get('has_contact_phone'):
            print("✅ Python方式有效: 联系人信息正确出现在文档中")
        else:
            print("⚠️ Python方式可能有问题: 联系人信息未出现在文档中")
    
    print("\n🎯 最终结论:")
    
    # 判断Java修复是否必要
    java_works = java_with_contact.get('success', False)
    python_works = python_result.get('success', False)
    
    if python_works and java_works:
        print("✅ 两种方式都正常工作")
        print("💡 Java修复提供了额外的保障和完整性")
        print("💡 对于Streamlit应用，Python层的修复已经足够")
        print("💡 Java修复对于直接调用Java程序是有益的")
    elif python_works and not java_works:
        print("✅ Python方式正常，Java方式有问题")
        print("💡 主要功能不受影响，因为Streamlit使用Python方式")
        print("💡 Java修复可以改善直接调用的体验")
    elif not python_works and java_works:
        print("⚠️ Python方式有问题，Java方式正常")
        print("💡 需要检查Python层的实现")
    else:
        print("❌ 两种方式都有问题")
        print("💡 需要进一步调试")
    
    print("\n💡 建议:")
    print("1. Java修复是有益的，但不是关键的")
    print("2. Python层的修复已经解决了主要问题")
    print("3. 编码问题可以后续解决，不影响核心功能")
    print("4. 当前的联系人信息流程已经基本完整")

if __name__ == "__main__":
    main()
