#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OpenAI格式API配置
Test OpenAI Format API Configuration
"""

import os
import sys
import requests
import json
import base64
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config.settings import app_settings


def test_api_connectivity():
    """测试API连通性"""
    print("🔗 测试302.ai API连通性...")
    
    try:
        # 简单的连通性测试
        response = requests.get(f"{app_settings.ai.API_302_BASE_URL}", timeout=10)
        print(f"✅ API基础连接正常: {response.status_code}")
        return True
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False


def test_openai_format_api():
    """测试OpenAI格式API配置"""
    print("\n🧪 测试OpenAI格式API配置...")
    print("=" * 50)
    
    try:
        url = f"{app_settings.ai.API_302_BASE_URL}/302/images/generations"
        
        headers = {
            'Authorization': f'Bearer {app_settings.ai.API_302_KEY}',
            'Content-Type': 'application/json'
        }
        
        # 简单的测试提示词
        payload = {
            "model": "hidream-i1-fast",
            "prompt": "A simple blue football team logo, modern design, circular shape",
            "n": 1,
            "size": "728x728",
            "response_format": "b64_json",
            "quality": "standard",
            "aspect_ratio": "1:1"
        }
        
        print(f"🌐 请求URL: {url}")
        print(f"🔑 API Key: {app_settings.ai.API_302_KEY[:20]}...")
        print(f"📝 请求参数: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        print("\n🚀 发送请求...")
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        
        print(f"📡 响应状态码: {response.status_code}")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 请求成功!")
            print(f"📊 响应数据结构: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if result.get("data") and len(result["data"]) > 0:
                image_data = result["data"][0]
                print(f"🖼️ 图片数据字段: {list(image_data.keys())}")
                
                # 检查base64数据
                b64_data = None
                for field in ["bs62_json", "b64_json", "bs64_json"]:
                    if field in image_data:
                        b64_data = image_data[field]
                        print(f"✅ 找到base64数据字段: {field}")
                        break
                
                if b64_data:
                    # 尝试解码并保存
                    try:
                        image_bytes = base64.b64decode(b64_data)
                        
                        # 保存测试图片
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        test_filename = f"test_openai_api_{timestamp}.png"
                        test_path = os.path.join("temp", test_filename)
                        
                        # 确保temp目录存在
                        os.makedirs("temp", exist_ok=True)
                        
                        with open(test_path, "wb") as f:
                            f.write(image_bytes)
                        
                        print(f"✅ 测试图片保存成功: {test_path}")
                        print(f"📏 图片大小: {len(image_bytes)} bytes")
                        return True
                        
                    except Exception as decode_error:
                        print(f"❌ base64解码失败: {decode_error}")
                        return False
                else:
                    print("❌ 未找到有效的base64数据字段")
                    return False
            else:
                print("❌ 响应中无图片数据")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"📄 错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_field_names():
    """测试不同的字段名"""
    print("\n🔍 测试API响应字段名...")
    
    # 模拟不同的响应格式
    test_responses = [
        {"bs62_json": "test_data_1"},
        {"b64_json": "test_data_2"}, 
        {"bs64_json": "test_data_3"},
        {"url": "https://example.com/image.png"}
    ]
    
    for i, response in enumerate(test_responses):
        print(f"📋 测试响应 {i+1}: {response}")
        
        # 模拟代码中的字段检查逻辑
        b64_data = response.get("bs62_json") or response.get("b64_json") or response.get("bs64_json")
        
        if b64_data:
            print(f"✅ 找到base64数据: {b64_data}")
        else:
            print("❌ 未找到base64数据")
        
        print()


def main():
    """主测试函数"""
    print("🚀 开始OpenAI格式API配置测试")
    print("=" * 60)
    
    # 显示配置信息
    print(f"🔧 当前配置:")
    print(f"   - API Base URL: {app_settings.ai.API_302_BASE_URL}")
    print(f"   - API Key: {app_settings.ai.API_302_KEY[:20]}...")
    print()
    
    test_results = []
    
    # 运行测试
    test_results.append(("API连通性", test_api_connectivity()))
    test_results.append(("字段名测试", test_field_names() or True))  # 这个测试总是返回True
    test_results.append(("OpenAI格式API", test_openai_format_api()))
    
    # 汇总结果
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！OpenAI格式API配置正确。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    main()
