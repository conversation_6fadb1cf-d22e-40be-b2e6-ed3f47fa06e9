#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最新生成的Word文档
"""

import zipfile
import xml.etree.ElementTree as ET
import os

def check_newest_word_document():
    """检查最新生成的Word文档"""
    print("🔍 检查最新生成的Word文档")
    print("=" * 60)
    
    word_file = "data/user_e358df6703a9/word_output/足球队_registration_1756545677635.docx"
    
    if not os.path.exists(word_file):
        print(f"❌ Word文件不存在: {word_file}")
        return False
    
    try:
        with zipfile.ZipFile(word_file, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
                
                # 提取文本内容
                try:
                    root = ET.fromstring(content)
                    text_content = []
                    for elem in root.iter():
                        if elem.text:
                            text_content.append(elem.text)
                    
                    full_text = ' '.join(text_content)
                    
                    print("📄 最新Word文档内容分析:")
                    
                    # 检查联系人信息
                    has_contact_person_zhaoliu = "赵六" in full_text
                    has_contact_phone = "18454432036" in full_text
                    
                    print(f"   联系人'赵六': {'✅ 找到' if has_contact_person_zhaoliu else '❌ 未找到'}")
                    print(f"   电话'18454432036': {'✅ 找到' if has_contact_phone else '❌ 未找到'}")
                    
                    # 显示联系人相关的上下文
                    print(f"\n📄 联系人相关内容:")
                    words = full_text.split()
                    contact_found = False
                    
                    for i, word in enumerate(words):
                        if "联系人" in word:
                            start = max(0, i-3)
                            end = min(len(words), i+15)
                            context = ' '.join(words[start:end])
                            print(f"   联系人上下文: {context}")
                            contact_found = True
                            break
                    
                    if not contact_found:
                        print("   ❌ 未找到联系人相关内容")
                    
                    # 最终结论
                    print(f"\n🎯 检查结果:")
                    
                    if has_contact_person_zhaoliu and has_contact_phone:
                        print("🎉 联系人信息修复成功！")
                        print("✅ 通过_auto_generate_word_document生成的Word包含联系人信息")
                        print("✅ 这证明我们的修复是有效的")
                        
                        print(f"\n💡 这说明问题可能在于:")
                        print(f"   1. Streamlit应用中的调用方式不同")
                        print(f"   2. 实际用户操作时的数据传递有问题")
                        
                        return True
                        
                    else:
                        print("❌ 联系人信息仍然缺失")
                        print("💡 需要进一步调试_auto_generate_word_document方法")
                        return False
                        
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 检查通过调试脚本生成的最新Word文档")
    print("=" * 70)
    
    result = check_newest_word_document()
    
    if result:
        print(f"\n🎉 重要发现!")
        print(f"✅ 通过调试脚本调用_auto_generate_word_document生成的Word包含联系人信息")
        print(f"❌ 但通过Streamlit应用生成的Word不包含联系人信息")
        
        print(f"\n💡 这说明问题在于:")
        print(f"   1. Streamlit应用中的实际调用路径与我们的调试不同")
        print(f"   2. 可能存在其他的Word生成路径")
        print(f"   3. 需要检查Streamlit应用中的实际调用代码")
        
        print(f"\n🎯 下一步:")
        print(f"   1. 检查Streamlit应用中Word生成的实际调用路径")
        print(f"   2. 确认是否有其他Word生成方法被调用")
        print(f"   3. 检查数据传递的完整链路")
    else:
        print(f"\n❌ 调试脚本生成的Word也不包含联系人信息")
        print(f"💡 需要进一步调试_auto_generate_word_document方法内部")

if __name__ == "__main__":
    main()
