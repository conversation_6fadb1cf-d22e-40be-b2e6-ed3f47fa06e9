#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的天依369工作流
"""

import os
import sys
import json
import zipfile
import re
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_real_tianyi369_workflow():
    """测试真实的天依369工作流"""
    print("=" * 60)
    print("🧪 测试真实的天依369工作流")
    print("=" * 60)
    
    try:
        # 读取真实的天依369数据
        user_data_file = "data/user_c61aa17e3868/enhanced_ai_data/天依369_ai_data.json"
        
        if not os.path.exists(user_data_file):
            print(f"❌ 数据文件不存在: {user_data_file}")
            return False
        
        with open(user_data_file, 'r', encoding='utf-8') as f:
            ai_data = json.load(f)
        
        print(f"📄 读取真实AI数据:")
        extracted_info = ai_data.get("extracted_info", {})
        basic_info = extracted_info.get("basic_info", {})
        kit_colors = extracted_info.get("kit_colors", {})
        additional_info = extracted_info.get("additional_info", {})
        
        print(f"   联系人: '{basic_info.get('contact_person', '')}'")
        print(f"   领队: '{basic_info.get('leader_name', '')}' {'❌ 空' if not basic_info.get('leader_name', '') else '✅'}")
        print(f"   教练: '{additional_info.get('coach_name', '')}' {'❌ 空' if not additional_info.get('coach_name', '') else '✅'}")
        print(f"   队医: '{basic_info.get('team_doctor', '')}' {'❌ 空' if not basic_info.get('team_doctor', '') else '✅'}")
        print(f"   球袜颜色: '{kit_colors.get('socks_color', '')}' {'❌ 空' if not kit_colors.get('socks_color', '') else '✅'}")
        
        # 使用真实的FashionWorkflowService
        from services.fashion_workflow_service import FashionWorkflowService
        
        # 创建一个简单的debug对象
        class SimpleDebug:
            def detailed_info(self, msg): print(f"   🔍 {msg}")
            def progress_info(self, msg): print(f"   📋 {msg}")
            def error(self, msg): print(f"   ❌ {msg}")
        
        debug = SimpleDebug()
        workflow_service = FashionWorkflowService(debug)
        
        # 模拟真实的工作流调用
        team_name = "天依369"
        user_id = "user_c61aa17e3868"
        
        print(f"\n📄 调用真实的工作流服务:")
        print(f"   团队名称: {team_name}")
        print(f"   用户ID: {user_id}")
        
        # 调用generate_word_report方法
        result = workflow_service.generate_word_report(team_name, user_id)
        
        if result['success']:
            output_file = result['file_path']
            print(f"\n✅ 真实工作流Word生成成功: {os.path.basename(output_file)}")
            
            # 分析生成的文档
            with zipfile.ZipFile(output_file, 'r') as zip_file:
                with zip_file.open('word/document.xml') as xml_file:
                    content = xml_file.read().decode('utf-8')
            
            # 检查关键数据是否出现
            keywords = ["天依369", "赵六", "粉色", "黑色", "绿色", "18454432036"]
            found_keywords = []
            missing_keywords = []
            
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append(keyword)
                    print(f"   ✅ 找到: '{keyword}'")
                else:
                    missing_keywords.append(keyword)
                    print(f"   ❌ 未找到: '{keyword}'")
            
            # 检查空单元格数量
            text_pattern = r'<w:t[^>]*>([^<]*)</w:t>'
            texts = re.findall(text_pattern, content)
            
            cell_pattern = r'<w:tc[^>]*>(.*?)</w:tc>'
            cells = re.findall(cell_pattern, content, re.DOTALL)
            
            empty_cells = 0
            filled_cells = 0
            
            for cell in cells:
                cell_texts = re.findall(text_pattern, cell)
                cell_content = ''.join(cell_texts).strip()
                
                if not cell_content:
                    empty_cells += 1
                else:
                    filled_cells += 1
            
            print(f"\n📊 真实工作流文档统计:")
            print(f"   总单元格数: {len(cells)}")
            print(f"   空单元格数: {empty_cells}")
            print(f"   填充单元格数: {filled_cells}")
            print(f"   找到关键词: {len(found_keywords)}/{len(keywords)}")
            
            # 提取有意义的文本内容
            meaningful_texts = []
            for text in texts:
                clean_text = text.strip()
                if clean_text and len(clean_text) > 0 and clean_text not in ['', ' ', '　']:
                    meaningful_texts.append(clean_text)
            
            print(f"\n📄 文档中的有意义内容（前20个）:")
            for i, text in enumerate(meaningful_texts[:20]):
                print(f"   {i+1}. '{text}'")
            
            # 检查是否包含"赵六"在多个位置（领队、教练、队医）
            zhao_liu_count = content.count("赵六")
            print(f"\n📊 '赵六'出现次数: {zhao_liu_count}")
            
            if zhao_liu_count >= 3:  # 应该在联系人、领队、教练、队医位置出现
                print(f"   ✅ '赵六'出现次数正常，自动填充生效")
                success = True
            else:
                print(f"   ❌ '赵六'出现次数不足，自动填充可能未生效")
                success = False
            
            # 检查颜色信息
            color_count = content.count("粉色") + content.count("黑色") + content.count("绿色")
            print(f"📊 颜色信息出现次数: {color_count}")
            
            if color_count >= 3:
                print(f"   ✅ 颜色信息正常")
            else:
                print(f"   ❌ 颜色信息不足")
                success = False
            
            return success
        else:
            print(f"\n❌ 真实工作流Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_with_old_document():
    """与旧文档比较"""
    print(f"\n" + "=" * 60)
    print("📊 与旧文档比较")
    print("=" * 60)
    
    try:
        # 查找最新的两个文档
        word_output_dir = "data/user_c61aa17e3868/word_output"
        
        if not os.path.exists(word_output_dir):
            print(f"❌ 输出目录不存在: {word_output_dir}")
            return
        
        word_files = [f for f in os.listdir(word_output_dir) if f.endswith('.docx')]
        word_files.sort(key=lambda x: os.path.getmtime(os.path.join(word_output_dir, x)), reverse=True)
        
        if len(word_files) >= 2:
            latest_file = os.path.join(word_output_dir, word_files[0])
            previous_file = os.path.join(word_output_dir, word_files[1])
            
            print(f"📄 最新文档: {word_files[0]}")
            print(f"📄 之前文档: {word_files[1]}")
            
            # 分析两个文档
            for label, file_path in [("最新", latest_file), ("之前", previous_file)]:
                with zipfile.ZipFile(file_path, 'r') as zip_file:
                    with zip_file.open('word/document.xml') as xml_file:
                        content = xml_file.read().decode('utf-8')
                
                zhao_liu_count = content.count("赵六")
                color_count = content.count("粉色") + content.count("黑色") + content.count("绿色")
                
                print(f"   {label}文档: '赵六'出现{zhao_liu_count}次, 颜色出现{color_count}次")
        else:
            print(f"📄 只有{len(word_files)}个文档，无法比较")
            
    except Exception as e:
        print(f"❌ 比较失败: {e}")

def main():
    """主函数"""
    print("🚀 测试真实的天依369工作流")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = test_real_tianyi369_workflow()
    
    # 比较文档
    compare_with_old_document()
    
    print("\n" + "=" * 60)
    print("📋 真实工作流测试总结")
    print("=" * 60)
    
    if success:
        print("🎉 真实工作流测试成功！")
        print("✅ 自动填充逻辑在真实环境中正常工作")
        print("✅ 空字段已被正确填充")
        print("✅ Word文档生成正常")
        print("\n💡 修复已生效！用户现在应该能看到完整填充的Word文档了！")
    else:
        print("❌ 真实工作流测试失败！")
        print("需要检查为什么修复在真实环境中没有生效")

if __name__ == "__main__":
    main()
