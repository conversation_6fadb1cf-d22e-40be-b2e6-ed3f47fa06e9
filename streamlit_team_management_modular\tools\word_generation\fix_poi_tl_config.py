#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复poi-tl配置以支持空格占位符的Java代码修改建议
"""

def analyze_poi_tl_issue():
    """分析poi-tl占位符问题"""
    print("🎯 poi-tl占位符问题分析")
    print("=" * 60)
    
    print("📄 问题根因:")
    print("1. Word模板中占位符被XML分割成多个<w:t>标签")
    print("2. poi-tl默认正则表达式不支持空格: [\\w\\u4e00-\\u9fa5]+")
    print("3. {{contact Person}} 包含空格，不匹配默认正则")
    
    print(f"\n📄 当前模板XML结构:")
    print("   {{</w:t></w:r><w:r><w:t>contact Person</w:t></w:r><w:r><w:t>}}")
    print("   ↓ poi-tl getText() 连接后:")
    print("   {{contact Person}}")
    print("   ↓ 默认正则匹配:")
    print("   ❌ 不匹配 (包含空格)")
    
    print(f"\n💡 解决方案:")
    
    print(f"\n方案1: 修复模板 (推荐)")
    print("   - 确保占位符不被XML分割")
    print("   - 使用标准格式: {{contactPerson}}, {{contactPhone}}")
    print("   - 关闭Word拼写检查避免分割")
    
    print(f"\n方案2: 修改Java代码支持空格")
    print("   - 修改poi-tl正则表达式配置")
    print("   - 支持空格: [\\w\\u4e00-\\u9fa5\\s]+")
    print("   - 修改Java字段映射")

def generate_java_fix_code():
    """生成Java修复代码"""
    print(f"\n🔧 Java代码修复方案")
    print("=" * 60)
    
    java_code = '''
// 方案2: 修改WordGeneratorCore.java以支持空格占位符

// 1. 修改poi-tl配置
Configure config = Configure.builder()
    .buildGrammerRegex("((#)?[\\\\w\\\\u4e00-\\\\u9fa5\\\\s]+(\\\\.[\\\\w\\\\u4e00-\\\\u9fa5\\\\s]+)*)?")  // 添加\\\\s支持空格
    .build();

XWPFTemplate template = XWPFTemplate.compile(templatePath, config);

// 2. 修改数据映射 - 支持两种格式
Map<String, Object> teamInfo = new HashMap<>();
teamInfo.put("contactPerson", contactPerson);
teamInfo.put("contactPhone", contactPhone);
// 同时支持空格格式
teamInfo.put("contact Person", contactPerson);
teamInfo.put("contact Phone", contactPhone);

// 3. 或者在JsonDataParser中添加字段映射
private void parseTeamInfo(JsonNode teamInfoNode, Map<String, Object> teamInfo) {
    // 现有代码...
    
    // 添加空格格式支持
    if (teamInfoNode.has("contactPerson")) {
        String contactPerson = teamInfoNode.get("contactPerson").asText();
        teamInfo.put("contactPerson", contactPerson);
        teamInfo.put("contact Person", contactPerson);  // 支持空格格式
    }
    
    if (teamInfoNode.has("contactPhone")) {
        String contactPhone = teamInfoNode.get("contactPhone").asText();
        teamInfo.put("contactPhone", contactPhone);
        teamInfo.put("contact Phone", contactPhone);  // 支持空格格式
    }
}
'''
    
    print("Java代码修改:")
    print(java_code)

def create_template_fix_guide():
    """创建模板修复指南"""
    print(f"\n📋 模板修复指南 (推荐方案)")
    print("=" * 60)
    
    guide = '''
🎯 Word模板修复步骤:

1. 关闭Word拼写检查:
   - 文件 → 选项 → 校对
   - 取消勾选"键入时检查拼写"
   - 取消勾选"键入时检查语法"

2. 删除现有联系人占位符:
   - 找到: 球队联系人：{{contact Person}}电话：{{contact Phone}}
   - 完全删除这行内容

3. 重新输入标准占位符:
   - 输入: 球队联系人：{{contactPerson}}电话：{{contactPhone}}
   - 一次性输入，不要分段
   - 不要复制粘贴

4. 验证占位符格式:
   - 保存文档
   - 重新打开检查
   - 确保没有红色下划线
   - 确保占位符显示为连续文本

5. 测试模板:
   - 使用测试数据生成Word文档
   - 检查联系人信息是否正确显示

✅ 正确格式: {{contactPerson}} {{contactPhone}}
❌ 错误格式: {{contact Person}} {{contact Phone}}
❌ 分割格式: {{</w:t></w:r><w:r><w:t>contactPerson</w:t></w:r><w:r><w:t>}}
'''
    
    print(guide)

def recommend_solution():
    """推荐解决方案"""
    print(f"\n🎯 推荐解决方案")
    print("=" * 60)
    
    print("基于poi-tl源码分析，推荐使用方案1：")
    print("")
    print("✅ 优点:")
    print("   - 符合poi-tl标准规范")
    print("   - 不需要修改Java代码")
    print("   - 性能最优")
    print("   - 兼容性最好")
    print("")
    print("🔧 具体操作:")
    print("   1. 关闭Word拼写检查")
    print("   2. 删除现有联系人占位符")
    print("   3. 重新输入: {{contactPerson}} {{contactPhone}}")
    print("   4. 确保占位符不被XML分割")
    print("")
    print("⚠️ 关键点:")
    print("   - 占位符必须在XML中是连续的")
    print("   - 不能包含空格（poi-tl默认不支持）")
    print("   - 使用驼峰命名法")

def main():
    """主函数"""
    analyze_poi_tl_issue()
    generate_java_fix_code()
    create_template_fix_guide()
    recommend_solution()
    
    print(f"\n📊 总结")
    print("=" * 60)
    print("🔍 问题确认: poi-tl正则表达式不支持空格占位符")
    print("🔧 推荐方案: 修复Word模板使用标准格式")
    print("💡 关键操作: 关闭拼写检查，重新输入占位符")
    print("✅ 预期结果: {{contactPerson}} {{contactPhone}} 正常工作")

if __name__ == "__main__":
    main()
