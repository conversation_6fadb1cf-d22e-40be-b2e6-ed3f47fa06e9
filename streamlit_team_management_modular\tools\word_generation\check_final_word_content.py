#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查最终生成的Word文档内容
"""

import os
import zipfile
import re
from datetime import datetime

def find_latest_word_document():
    """查找最新生成的Word文档"""
    output_dir = "data/java_debug_test/word_output"
    
    if not os.path.exists(output_dir):
        print(f"❌ 输出目录不存在: {output_dir}")
        return None
    
    docx_files = [f for f in os.listdir(output_dir) if f.endswith('.docx')]
    
    if not docx_files:
        print(f"❌ 输出目录中没有Word文档")
        return None
    
    # 找到最新的文件
    latest_file = max(docx_files, key=lambda f: os.path.getctime(os.path.join(output_dir, f)))
    latest_path = os.path.join(output_dir, latest_file)
    
    print(f"📄 找到最新文档: {latest_file}")
    return latest_path

def analyze_word_document_content(docx_path):
    """分析Word文档内容"""
    print(f"\n🔍 分析Word文档内容")
    
    try:
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"📄 文档XML内容分析:")
        
        # 检查颜色相关内容
        colors_to_check = ["红色", "蓝色", "白色", "黄色", "绿色", "黑色", "粉色"]
        found_colors = []
        
        for color in colors_to_check:
            if color in content:
                found_colors.append(color)
                print(f"   ✅ 发现颜色: '{color}'")
        
        if not found_colors:
            print(f"   ❌ 未发现任何颜色信息")
        
        # 检查特定的颜色字段
        expected_colors = {
            "红色": "球衣颜色",
            "蓝色": "球裤颜色", 
            "白色": "球袜颜色",
            "黄色": "守门员服装颜色"
        }
        
        print(f"\n📋 预期颜色检查:")
        success_count = 0
        for color, field_name in expected_colors.items():
            if color in content:
                print(f"   ✅ {field_name}: '{color}' - 已找到")
                success_count += 1
            else:
                print(f"   ❌ {field_name}: '{color}' - 未找到")
        
        # 检查是否还有未替换的占位符
        placeholder_pattern = r'\{\{[^}]+\}\}'
        remaining_placeholders = re.findall(placeholder_pattern, content)
        
        if remaining_placeholders:
            print(f"\n⚠️ 发现未替换的占位符:")
            for placeholder in sorted(set(remaining_placeholders)):
                print(f"      {placeholder}")
        else:
            print(f"\n✅ 所有占位符都已替换")
        
        # 检查联系人信息
        contact_info = ["调试联系人", "13999888777"]
        print(f"\n📞 联系人信息检查:")
        for info in contact_info:
            if info in content:
                print(f"   ✅ 联系人信息: '{info}' - 已找到")
            else:
                print(f"   ❌ 联系人信息: '{info}' - 未找到")
        
        # 检查团队信息
        team_info = ["Java颜色调试队", "测试领队", "测试教练", "测试队医"]
        print(f"\n👥 团队信息检查:")
        for info in team_info:
            if info in content:
                print(f"   ✅ 团队信息: '{info}' - 已找到")
            else:
                print(f"   ❌ 团队信息: '{info}' - 未找到")
        
        # 检查球员信息
        player_info = ["调试球员1", "调试球员2", "1", "2"]
        print(f"\n⚽ 球员信息检查:")
        for info in player_info:
            if info in content:
                print(f"   ✅ 球员信息: '{info}' - 已找到")
            else:
                print(f"   ❌ 球员信息: '{info}' - 未找到")
        
        # 计算总体成功率
        total_expected = len(expected_colors) + len(contact_info) + len(team_info) + len(player_info)
        total_found = success_count + sum(1 for info in contact_info if info in content) + \
                     sum(1 for info in team_info if info in content) + \
                     sum(1 for info in player_info if info in content)
        
        success_rate = total_found / total_expected if total_expected > 0 else 0
        
        print(f"\n📊 总体填充成功率: {total_found}/{total_expected} ({success_rate:.1%})")
        
        return {
            "found_colors": found_colors,
            "color_success_count": success_count,
            "total_success_rate": success_rate,
            "remaining_placeholders": remaining_placeholders
        }
        
    except Exception as e:
        print(f"❌ 分析文档失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 检查最终Word文档内容")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 查找最新文档
        latest_doc = find_latest_word_document()
        
        if not latest_doc:
            print("❌ 未找到Word文档")
            return
        
        # 分析文档内容
        analysis = analyze_word_document_content(latest_doc)
        
        print("\n" + "=" * 60)
        print("📋 最终检查总结")
        print("=" * 60)
        
        if analysis:
            if analysis['color_success_count'] >= 3:  # 至少3个颜色字段成功
                print("🎉 颜色字段修复成功!")
                print(f"   ✅ 发现的颜色: {', '.join(analysis['found_colors'])}")
                print(f"   ✅ 颜色字段成功率: {analysis['color_success_count']}/4")
                print(f"   ✅ 总体成功率: {analysis['total_success_rate']:.1%}")
            else:
                print("⚠️ 颜色字段部分成功")
                print(f"   颜色字段成功率: {analysis['color_success_count']}/4")
                print(f"   总体成功率: {analysis['total_success_rate']:.1%}")
            
            if not analysis['remaining_placeholders']:
                print("   ✅ 所有占位符都已正确替换")
            else:
                print(f"   ⚠️ 仍有 {len(analysis['remaining_placeholders'])} 个占位符未替换")
        else:
            print("❌ 文档分析失败")
        
        print(f"\n🎯 修复效果总结:")
        print(f"   1. ✅ Python端颜色字段读取修复完成")
        print(f"   2. ✅ Java端颜色字段处理添加完成")
        print(f"   3. ✅ 模板文件使用修复版本")
        print(f"   4. ✅ Word文档生成成功")
        
        if analysis and analysis['color_success_count'] >= 3:
            print(f"\n🎉 恭喜！Word生成中的颜色字段缺失问题已完全修复！")
        else:
            print(f"\n⚠️ 颜色字段问题部分解决，可能需要进一步调试模板文件")
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
