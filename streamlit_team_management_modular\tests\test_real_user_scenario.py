#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实用户使用场景的自动填充
"""

import os
import sys
import json
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.ai_service import AIService
from word_generator_service import WordGeneratorService
from config.settings import app_settings

def create_real_user_scenario():
    """创建真实用户场景"""
    print("=" * 60)
    print("🔍 创建真实用户场景测试")
    print("=" * 60)
    
    # 模拟用户输入：只提供基本信息
    user_input = {
        "contact_person": "李小明",
        "contact_phone": "13912345678", 
        "jersey_color": "粉色"
    }
    
    print(f"📄 用户输入（最少信息）:")
    for key, value in user_input.items():
        print(f"   {key}: '{value}'")
    
    # 1. AI服务处理
    ai_service = AIService()
    enhanced_info = ai_service._apply_smart_fill_logic(user_input.copy())
    
    print(f"\n📄 AI服务自动填充后:")
    for key, value in enhanced_info.items():
        print(f"   {key}: '{value}'")
    
    # 2. 模拟保存到AI导出数据
    ai_export_data = {
        "team_info": {
            "name": "粉色测试队",
            "ai_extracted_info": {
                "basic_info": {
                    "team_name": "粉色测试队",
                    "contact_person": enhanced_info.get("contact_person", ""),
                    "contact_phone": enhanced_info.get("contact_phone", ""),
                    "leader_name": enhanced_info.get("leader_name", "自动填充"),
                    "team_doctor": enhanced_info.get("team_doctor", "自动填充")
                },
                "kit_colors": {
                    "jersey_color": enhanced_info.get("jersey_color", ""),
                    "shorts_color": enhanced_info.get("shorts_color", ""),
                    "socks_color": enhanced_info.get("socks_color", ""),
                    "goalkeeper_kit_color": enhanced_info.get("goalkeeper_kit_color", "")
                },
                "additional_info": {
                    "coach_name": "自动填充"
                }
            }
        }
    }
    
    print(f"\n📄 AI导出数据结构:")
    basic_info = ai_export_data["team_info"]["ai_extracted_info"]["basic_info"]
    kit_colors = ai_export_data["team_info"]["ai_extracted_info"]["kit_colors"]
    additional_info = ai_export_data["team_info"]["ai_extracted_info"]["additional_info"]
    
    print(f"   basic_info:")
    for key, value in basic_info.items():
        print(f"      {key}: '{value}'")
    print(f"   kit_colors:")
    for key, value in kit_colors.items():
        print(f"      {key}: '{value}'")
    print(f"   additional_info:")
    for key, value in additional_info.items():
        print(f"      {key}: '{value}'")
    
    return ai_export_data

def test_workflow_processing(ai_export_data):
    """测试工作流处理"""
    print(f"\n" + "=" * 60)
    print("🔍 测试工作流处理")
    print("=" * 60)
    
    # 模拟工作流的数据合并逻辑
    ai_extracted_info = ai_export_data["team_info"]["ai_extracted_info"]
    basic_info = ai_extracted_info.get("basic_info", {})
    kit_colors = ai_extracted_info.get("kit_colors", {})
    additional_info = ai_extracted_info.get("additional_info", {})
    
    def is_valid_value(value):
        """检查值是否有效（不是占位符）"""
        if not value or value in ["待定", "未知", "暂无", ""]:
            return False
        return True

    def auto_fill_with_contact(value, contact_person):
        """自动填充逻辑：如果值是'自动填充'，则使用联系人信息"""
        if value == "自动填充":
            return contact_person
        elif is_valid_value(value):
            return value
        return None
    
    # 获取联系人信息
    contact_person = basic_info.get("contact_person", "")
    
    # 应用自动填充逻辑
    team_data = {"name": "粉色测试队"}
    
    # 联系人信息
    if is_valid_value(contact_person):
        team_data["contact_person"] = contact_person
    if is_valid_value(basic_info.get("contact_phone")):
        team_data["contact_phone"] = basic_info.get("contact_phone")
    
    # 人员信息自动填充逻辑
    leader_value = auto_fill_with_contact(basic_info.get("leader_name"), contact_person)
    if leader_value:
        team_data["leader"] = leader_value
    
    coach_value = auto_fill_with_contact(additional_info.get("coach_name"), contact_person)
    if coach_value:
        team_data["coach"] = coach_value
    
    doctor_value = auto_fill_with_contact(basic_info.get("team_doctor"), contact_person)
    if doctor_value:
        team_data["doctor"] = doctor_value
    
    # 颜色字段合并
    for color_field in ["jersey_color", "shorts_color", "socks_color", "goalkeeper_kit_color"]:
        if is_valid_value(kit_colors.get(color_field)):
            team_data[color_field] = kit_colors.get(color_field)
    
    print(f"📄 工作流处理后的team_data:")
    for key, value in team_data.items():
        print(f"   {key}: '{value}'")
    
    # 验证自动填充效果
    print(f"\n📋 自动填充验证:")
    expected_contact = "李小明"
    
    checks = [
        ("联系人信息", team_data.get("contact_person") == expected_contact),
        ("领队自动填充", team_data.get("leader") == expected_contact),
        ("教练自动填充", team_data.get("coach") == expected_contact),
        ("队医自动填充", team_data.get("doctor") == expected_contact),
        ("球衣颜色", team_data.get("jersey_color") == "粉色"),
        ("球裤颜色", team_data.get("shorts_color") == "黑色"),
        ("球袜颜色", team_data.get("socks_color") == "粉色"),
        ("守门员服装颜色", team_data.get("goalkeeper_kit_color") == "绿色")
    ]
    
    success_count = 0
    for check_name, is_success in checks:
        status = "✅" if is_success else "❌"
        print(f"   {status} {check_name}")
        if is_success:
            success_count += 1
    
    success_rate = success_count / len(checks)
    print(f"\n📊 自动填充成功率: {success_count}/{len(checks)} ({success_rate:.1%})")
    
    return team_data, success_rate

def test_word_generation(team_data):
    """测试Word生成"""
    print(f"\n" + "=" * 60)
    print("🔍 测试Word生成")
    print("=" * 60)
    
    # 添加球员数据
    players_data = [
        {"name": "球员1", "jersey_number": "1", "photo": ""},
        {"name": "球员2", "jersey_number": "2", "photo": ""}
    ]
    
    try:
        # 创建WordGeneratorService
        paths = app_settings.word_generator.get_absolute_paths("real_user_test", app_settings.paths)
        word_service = WordGeneratorService(
            jar_path=paths['jar_path'],
            template_path=paths['template_path'],
            output_dir=paths['output_dir']
        )
        
        print(f"🚀 生成Word文档...")
        result = word_service.generate_report(team_data, players_data)
        
        if result['success']:
            output_file = result['file_path']
            print(f"✅ Word生成成功: {os.path.basename(output_file)}")
            
            # 检查生成的文档内容
            return check_word_content(output_file, team_data)
        else:
            print(f"❌ Word生成失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ Word生成测试失败: {e}")
        return False

def check_word_content(docx_path, expected_data):
    """检查Word文档内容"""
    try:
        import zipfile
        
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            with zip_file.open('word/document.xml') as xml_file:
                content = xml_file.read().decode('utf-8')
        
        print(f"\n📄 Word文档内容检查:")
        
        # 检查关键信息
        checks = [
            ("团队名称", "粉色测试队" in content),
            ("联系人", expected_data.get("contact_person", "") in content),
            ("联系电话", expected_data.get("contact_phone", "") in content),
            ("领队", expected_data.get("leader", "") in content),
            ("教练", expected_data.get("coach", "") in content),
            ("队医", expected_data.get("doctor", "") in content),
            ("球衣颜色", expected_data.get("jersey_color", "") in content),
            ("球裤颜色", expected_data.get("shorts_color", "") in content),
            ("球袜颜色", expected_data.get("socks_color", "") in content),
            ("守门员服装颜色", expected_data.get("goalkeeper_kit_color", "") in content)
        ]
        
        success_count = 0
        for check_name, is_found in checks:
            status = "✅" if is_found else "❌"
            print(f"   {status} {check_name}")
            if is_found:
                success_count += 1
        
        success_rate = success_count / len(checks)
        print(f"\n📊 Word内容完整率: {success_count}/{len(checks)} ({success_rate:.1%})")
        
        return success_rate >= 0.8  # 80%以上认为成功
        
    except Exception as e:
        print(f"❌ 检查Word内容失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 真实用户场景自动填充测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 1. 创建真实用户场景
        ai_export_data = create_real_user_scenario()
        
        # 2. 测试工作流处理
        team_data, auto_fill_rate = test_workflow_processing(ai_export_data)
        
        # 3. 测试Word生成
        word_success = test_word_generation(team_data)
        
        print("\n" + "=" * 60)
        print("📋 真实场景测试总结")
        print("=" * 60)
        
        print(f"✅ 测试场景: 用户只输入联系人、电话、球衣颜色")
        print(f"📊 自动填充成功率: {auto_fill_rate:.1%}")
        print(f"📄 Word生成: {'✅ 成功' if word_success else '❌ 失败'}")
        
        if auto_fill_rate >= 0.8 and word_success:
            print(f"\n🎉 真实用户场景测试完全成功!")
            print(f"   ✅ 人员自动填充: 联系人信息自动填充到领队、教练、队医")
            print(f"   ✅ 颜色自动生成: 根据球衣颜色自动生成其他颜色")
            print(f"   ✅ Word生成: 所有信息正确显示在Word文档中")
        else:
            print(f"\n⚠️ 真实用户场景测试部分成功")
            print(f"   自动填充率: {auto_fill_rate:.1%}")
            print(f"   Word生成: {'成功' if word_success else '失败'}")
        
        print(f"\n💡 用户使用指南:")
        print(f"   1. 用户只需输入: 联系人姓名、电话、球衣颜色")
        print(f"   2. 系统自动填充: 领队、教练、队医（使用联系人姓名）")
        print(f"   3. 系统自动生成: 球裤、球袜、守门员服装颜色")
        print(f"   4. 生成完整的Word报名表")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
